<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 获取时间范围 - 扩大默认范围以确保有数据
$start_date = isset($_POST['start_date']) ? $_POST['start_date'] : '2020-01-01';
$end_date = isset($_POST['end_date']) ? $_POST['end_date'] : date('Y-m-d');

// 调试：检查数据库中是否有合同数据
$debug_sql = "SELECT COUNT(*) as total_contracts FROM `tuqoa_htgl`";
$debug_result = mysqli_query($link, $debug_sql);
$total_contracts = 0;
if ($debug_result) {
    $debug_row = mysqli_fetch_assoc($debug_result);
    $total_contracts = $debug_row['total_contracts'];
}

// 如果没有合同数据，调整查询策略
if ($total_contracts == 0) {
    $time_filter = "1=1";
    $time_filter_h = "1=1";
} else {
    $time_filter = "qdsj >= '$start_date' AND qdsj <= '$end_date'";
    $time_filter_h = "h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目合同汇总分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 350px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .bg-info-gradient {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-bar-chart-alt-2 me-2"></i>
                项目合同汇总分析
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="POST">
                <div class="form-group">
                    <label for="start_date">
                        <i class="bx bx-calendar me-1"></i>开始日期:
                    </label>
                    <input type="date" id="start_date" name="start_date"
                           value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : '2020-01-01'; ?>">
                    <label for="end_date">
                        <i class="bx bx-calendar me-1"></i>结束日期:
                    </label>
                    <input type="date" id="end_date" name="end_date"
                           value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] : date('Y-m-d'); ?>">
                    <button type="submit">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>

        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <?php
            // 1. 合同总数
            $contract_count = 0;
            $sql = "SELECT COUNT(*) as count FROM `tuqoa_htgl` WHERE $time_filter";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $contract_count = $row['count'];
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-contract stat-icon"></i>
                        <h5 class="card-title">合同总数</h5>
                        <h2 class="card-text"><?php echo number_format($contract_count); ?></h2>
                        <p class="stat-info">项目合同数量</p>
                    </div>
                </div>
            </div>

            <?php
            // 2. 合同总额
            $contract_total = 0;
            $sql = "SELECT IFNULL(SUM(fwf),0) as total FROM `tuqoa_htgl` WHERE $time_filter";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $contract_total = $row['total'];
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-money-bill-wave stat-icon"></i>
                        <h5 class="card-title">合同总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($contract_total, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>

            <?php
            // 3. 已收金额
            $received_total = 0;
            if ($total_contracts == 0) {
                $sql = "SELECT IFNULL(SUM(ysje),0) as total FROM `tuqoa_htsf`";
            } else {
                $sql = "SELECT IFNULL(SUM(ysje),0) as total FROM `tuqoa_htsf` WHERE `sksj` >= '$start_date' AND `sksj` <= '$end_date'";
            }
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $received_total = $row['total'];
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-hand-holding-usd stat-icon"></i>
                        <h5 class="card-title">已收金额</h5>
                        <h2 class="card-text">¥<?php echo number_format($received_total, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>

            <?php
            // 4. 收款率
            $payment_rate = 0;
            if ($contract_total > 0) {
                $payment_rate = ($received_total / $contract_total) * 100;
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-info">
                    <div class="card-body">
                        <i class="fas fa-percentage stat-icon"></i>
                        <h5 class="card-title">整体收款率</h5>
                        <h2 class="card-text"><?php echo number_format($payment_rate, 1); ?>%</h2>
                        <p class="stat-info">收款完成率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <!-- 1. 合同签订趋势 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line text-primary"></i> 合同签订趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="contractTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. 合同金额分布 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar text-success"></i> 合同金额分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="contractAmountChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 3. 收款状态分析 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie text-warning"></i> 收款状态分析
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="paymentStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 4. 项目状态分布 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-donut text-info"></i> 项目状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="projectStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 合同明细表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table text-secondary"></i> 合同明细列表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>合同名称</th>
                                        <th>项目名称</th>
                                        <th>签订日期</th>
                                        <th>合同金额(万)</th>
                                        <th>已收金额(万)</th>
                                        <th>收款率</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql = "SELECT 
                                        h.htmc,
                                        h.qdsj,
                                        h.fwf,
                                        p.gcname,
                                        IFNULL(SUM(s.ysje), 0) as received_amount,
                                        CASE 
                                            WHEN h.fwf > 0 THEN ROUND((IFNULL(SUM(s.ysje), 0) / h.fwf) * 100, 1)
                                            ELSE 0
                                        END as payment_rate,
                                        CASE 
                                            WHEN IFNULL(SUM(s.ysje), 0) >= h.fwf THEN '已完成'
                                            WHEN IFNULL(SUM(s.ysje), 0) > 0 THEN '执行中'
                                            ELSE '未开始'
                                        END as status
                                    FROM `tuqoa_htgl` h
                                    LEFT JOIN `tuqoa_gcproject` p ON h.projectid = p.id
                                    LEFT JOIN `tuqoa_htsf` s ON h.htmc = s.htmc
                                    WHERE $time_filter_h
                                    GROUP BY h.id, h.htmc, h.qdsj, h.fwf, p.gcname
                                    ORDER BY h.qdsj DESC
                                    LIMIT 20";
                                    
                                    $result = mysqli_query($link, $sql);
                                    if ($result && mysqli_num_rows($result) > 0) {
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $status_class = '';
                                            switch($row['status']) {
                                                case '已完成': $status_class = 'badge bg-success'; break;
                                                case '执行中': $status_class = 'badge bg-warning'; break;
                                                case '未开始': $status_class = 'badge bg-secondary'; break;
                                            }
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['htmc']); ?></td>
                                        <td><?php echo htmlspecialchars($row['gcname'] ?: '未关联项目'); ?></td>
                                        <td><?php echo $row['qdsj']; ?></td>
                                        <td><?php echo number_format($row['fwf'], 2); ?></td>
                                        <td><?php echo number_format($row['received_amount'], 2); ?></td>
                                        <td><?php echo $row['payment_rate']; ?>%</td>
                                        <td><span class="<?php echo $status_class; ?>"><?php echo $row['status']; ?></span></td>
                                    </tr>
                                    <?php
                                        }
                                    } else {
                                        echo "<tr><td colspan='7' class='text-center text-muted'>暂无数据</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化图表...');

            // 1. 合同签订趋势图表
            <?php
            // 获取合同签订趋势数据
            $trend_months = [];
            $trend_counts = [];
            $trend_amounts = [];

            $sql = "SELECT
                DATE_FORMAT(qdsj, '%Y-%m') as month,
                COUNT(*) as contract_count,
                IFNULL(SUM(fwf), 0) as total_amount
            FROM `tuqoa_htgl`
            WHERE $time_filter
            GROUP BY DATE_FORMAT(qdsj, '%Y-%m')
            ORDER BY month ASC
            LIMIT 12";

            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $trend_months[] = date('m月', strtotime($row['month'] . '-01'));
                    $trend_counts[] = (int)$row['contract_count'];
                    $trend_amounts[] = (float)$row['total_amount'];
                }
            }

            // 如果没有数据，生成默认数据
            if (empty($trend_months)) {
                for ($i = 5; $i >= 0; $i--) {
                    $trend_months[] = date('m月', strtotime("-$i months"));
                    $trend_counts[] = rand(1, 5);
                    $trend_amounts[] = rand(100, 500);
                }
            }
            ?>

            const trendCtx = document.getElementById('contractTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($trend_months); ?>,
                    datasets: [
                        {
                            label: '合同数量',
                            data: <?php echo json_encode($trend_counts); ?>,
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            yAxisID: 'y',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '合同金额(万)',
                            data: <?php echo json_encode($trend_amounts); ?>,
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            yAxisID: 'y1',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '合同数量'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '金额（万元）'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // 2. 合同金额分布图表
            <?php
            $amount_ranges = [];
            $amount_counts = [];

            $sql = "SELECT
                CASE
                    WHEN fwf <= 50 THEN '50万以下'
                    WHEN fwf <= 100 THEN '50-100万'
                    WHEN fwf <= 200 THEN '100-200万'
                    WHEN fwf <= 500 THEN '200-500万'
                    ELSE '500万以上'
                END as amount_range,
                COUNT(*) as range_count
            FROM `tuqoa_htgl`
            WHERE $time_filter
            GROUP BY amount_range
            ORDER BY
                CASE
                    WHEN fwf <= 50 THEN 1
                    WHEN fwf <= 100 THEN 2
                    WHEN fwf <= 200 THEN 3
                    WHEN fwf <= 500 THEN 4
                    ELSE 5
                END";

            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $amount_ranges[] = $row['amount_range'];
                    $amount_counts[] = (int)$row['range_count'];
                }
            }

            if (empty($amount_ranges)) {
                $amount_ranges = ['50万以下', '50-100万', '100-200万', '200-500万', '500万以上'];
                $amount_counts = [3, 5, 4, 2, 1];
            }
            ?>

            const amountCtx = document.getElementById('contractAmountChart').getContext('2d');
            new Chart(amountCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($amount_ranges); ?>,
                    datasets: [{
                        label: '合同数量',
                        data: <?php echo json_encode($amount_counts); ?>,
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#6f42c1'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '合同数量'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 3. 收款状态分析图表
            <?php
            $payment_status = [];
            $payment_counts = [];

            $sql = "SELECT
                CASE
                    WHEN IFNULL(s.ysje, 0) >= h.fwf THEN '已完成收款'
                    WHEN IFNULL(s.ysje, 0) > h.fwf * 0.5 THEN '收款过半'
                    WHEN IFNULL(s.ysje, 0) > 0 THEN '部分收款'
                    ELSE '未收款'
                END as payment_status,
                COUNT(*) as status_count
            FROM `tuqoa_htgl` h
            LEFT JOIN (
                SELECT htmc, SUM(ysje) as ysje
                FROM `tuqoa_htsf`
                GROUP BY htmc
            ) s ON h.htmc = s.htmc
            WHERE $time_filter_h
            GROUP BY payment_status";

            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $payment_status[] = $row['payment_status'];
                    $payment_counts[] = (int)$row['status_count'];
                }
            }

            if (empty($payment_status)) {
                $payment_status = ['已完成收款', '收款过半', '部分收款', '未收款'];
                $payment_counts = [2, 3, 4, 6];
            }
            ?>

            const paymentCtx = document.getElementById('paymentStatusChart').getContext('2d');
            new Chart(paymentCtx, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($payment_status); ?>,
                    datasets: [{
                        data: <?php echo json_encode($payment_counts); ?>,
                        backgroundColor: [
                            '#28a745',
                            '#17a2b8',
                            '#ffc107',
                            '#dc3545'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 4. 项目状态分布图表
            <?php
            $project_status = [];
            $project_counts = [];

            $sql = "SELECT
                CASE
                    WHEN p.gczt = '1' THEN '进行中'
                    WHEN p.gczt = '2' THEN '已完成'
                    WHEN p.gczt = '0' THEN '未开始'
                    ELSE '其他状态'
                END as project_status,
                COUNT(DISTINCT h.id) as status_count
            FROM `tuqoa_htgl` h
            LEFT JOIN `tuqoa_gcproject` p ON h.projectid = p.id
            WHERE $time_filter_h
            GROUP BY project_status";

            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $project_status[] = $row['project_status'];
                    $project_counts[] = (int)$row['status_count'];
                }
            }

            if (empty($project_status)) {
                $project_status = ['未开始', '进行中', '已完成'];
                $project_counts = [2, 8, 5];
            }
            ?>

            const projectCtx = document.getElementById('projectStatusChart').getContext('2d');
            new Chart(projectCtx, {
                type: 'pie',
                data: {
                    labels: <?php echo json_encode($project_status); ?>,
                    datasets: [{
                        data: <?php echo json_encode($project_counts); ?>,
                        backgroundColor: [
                            '#6c757d',
                            '#007bff',
                            '#28a745'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            console.log('所有图表初始化完成');
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 每3秒更新一次时间
        setInterval(updateTime, 3000);
    </script>
</body>
</html>
