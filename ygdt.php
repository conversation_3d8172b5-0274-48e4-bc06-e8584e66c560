<?php
include '../config.php';

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工动态 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            height: 400px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-user me-2"></i>
                员工动态
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
           
            <div class="row">
                <?php
                // 查询员工总数
                $zrs = 0;
                $sql="SELECT COUNT(*) as zrs FROM `tuqoa_userinfo` where state<>5";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $zrs=$row["zrs"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <i class="fas fa-users stat-icon"></i>
                            <h5 class="card-title">员工总数</h5>
                            <h2 class="card-text"><?php echo $zrs; ?></h2>
                            <p class="stat-info">在职员工总数</p>
                        </div>
                    </div>
                </div>
                <?php
                // 查询本月入职人数
                $byrz = 0;
                $sql="SELECT count(*) as byrz FROM `tuqoa_userinfo` WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $byrz=$row["byrz"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <i class="fas fa-user-plus stat-icon"></i>
                            <h5 class="card-title">本月入职</h5>
                            <h2 class="card-text"><?php echo $byrz; ?></h2>
                            <p class="stat-info">新入职员工</p>
                        </div>
                    </div>
                </div>
                <?php
                // 查询本月转正人数
                $byzz = 0;
                $sql="SELECT count(*) as byzz FROM `tuqoa_userinfo` WHERE `positivedt`>='$startDate' and `positivedt`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $byzz=$row["byzz"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <i class="fas fa-user-check stat-icon"></i>
                            <h5 class="card-title">本月转正</h5>
                            <h2 class="card-text"><?php echo $byzz; ?></h2>
                            <p class="stat-info">转正员工数</p>
                        </div>
                    </div>
                </div>
                <?php
                // 查询本月晋升人数（这里假设有晋升相关字段，如果没有则显示0）
                $byjs = 0;
                // 如果有晋升相关的表或字段，可以在这里查询
                // $sql="SELECT count(*) as byjs FROM `tuqoa_userinfo` WHERE `promotion_date`>='$startDate' and `promotion_date`<='$endDate'";
                // $result = mysqli_query($link, $sql);
                // if ($result) {
                //     while ($row = mysqli_fetch_assoc($result)) {
                //         $byjs=$row["byjs"];
                //     }
                // }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <i class="fas fa-arrow-up stat-icon"></i>
                            <h5 class="card-title">本月晋升</h5>
                            <h2 class="card-text"><?php echo $byjs; ?></h2>
                            <p class="stat-info">晋升员工数</p>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 查找最近六个月的离职和入职人员
            $sql = "SELECT DATE_FORMAT(workdate, '%Y-%m') AS month, COUNT(*) AS new_employees, 0 AS quit_employees FROM tuqoa_userinfo WHERE workdate >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH) GROUP BY DATE_FORMAT(workdate, '%Y-%m') UNION ALL SELECT DATE_FORMAT(quitdt, '%Y-%m') AS month, 0 AS new_employees, COUNT(*) AS quit_employees FROM tuqoa_userinfo WHERE quitdt >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH) AND quitdt IS NOT NULL GROUP BY DATE_FORMAT(quitdt, '%Y-%m') ORDER BY month";
            $result = mysqli_query($link, $sql);

            // 初始化数组
            $months = [];
            $newEmployeesData = [];
            $quitEmployeesData = [];

            // 处理查询结果
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                $month = $row["month"];
                
                if (!in_array($month, $months)) {
                    $months[] = $month;
                }
                
                if ($row["new_employees"] > 0) {
                    $newEmployeesData[$month] = $row["new_employees"];
                }
                
                if ($row["quit_employees"] > 0) {
                    $quitEmployeesData[$month] = $row["quit_employees"];
                }
                } // End while loop
            } else {
                // 如果查询失败，使用默认数据
                $months = [];
                $completeNewData = [];
                $completeQuitData = [];
                $shortMonths = [];
            }
            
            // 确保所有月份都有数据（填充0）
            $completeNewData = [];
            $completeQuitData = [];
            foreach ($months as $month) {
                $completeNewData[] = isset($newEmployeesData[$month]) ? $newEmployeesData[$month] : 0;
                $completeQuitData[] = isset($quitEmployeesData[$month]) ? $quitEmployeesData[$month] : 0;
            }
            
            // 简化月份标签为MM格式
            $shortMonths = array_map(function($date) {
                return substr($date, -2); // 提取后两位
            }, $months);
            ?>
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">员工变动趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="employeeTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                //分析员工比例
                $bmmchj=[];
                $bmrshj=[];
                $sql = "SELECT deptname AS bmmc, COUNT(*) AS rshj FROM tuqoa_userinfo where state=1 GROUP BY deptname ORDER BY rshj DESC";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $bmmchj[]=$row["bmmc"]; 
                    $bmrshj[]= $row["rshj"];
                }
                $deptData1 = [
                    'labels' => $bmmchj,
                    'data' => $bmrshj
                ];
                ?>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">员工结构分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="employeeStructureChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">员工变动明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>姓名</th>
                                            <th>变动类型</th>
                                            <th>职务</th>
                                            <th>人事专业</th>
                                            <th>所学专业</th>
                                            <th>职称</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT * FROM `tuqoa_userinfo` WHERE (`workdate`>='$startDate' and `workdate`<='$endDate') or (`quitdt`>='$startDate' and `quitdt`<='$endDate' and `quitdt` IS NOT NULL)";
                                        $result = mysqli_query($link, $sql);

                                        if ($result && mysqli_num_rows($result) > 0) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $bhqk = ($row["state"] == 5) ? "离职" : "入职";
                                        ?>
                                        <tr>
                                            <td><a href="/task.php?a=p&num=userinfo&mid=<?=$row["id"]?>" target="_blank"><?=$row["name"]?></a></td>
                                            <td><?=$bhqk?></td>
                                            <td><?=$row["ranking"]?></td>
                                            <td><?=$row["rszy"]?></td>
                                            <td><?=$row["sszy"]?></td>
                                            <td><?=$row["zhicheng"]?></td>
                                        </tr>
                                        <?php
                                            } // end while
                                        } // end if
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
mysqli_close($link);
?>
    
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
    // 员工变动趋势图表
            const trendCtx = document.getElementById('employeeTrendChart').getContext('2d');
            
            // 使用处理后的数据
            const labels = <?php echo json_encode($shortMonths); ?>;
            const new_employees = <?php echo json_encode($completeNewData); ?>;
            const quit_employees = <?php echo json_encode($completeQuitData); ?>;
            
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '入职',
                        data: new_employees,
                        borderColor: '#1e88e5',
                        backgroundColor: 'rgba(30, 136, 229, 0.1)',
                        fill: true,
                        tension: 0.1
                    }, {
                        label: '离职',
                        data: quit_employees,
                        borderColor: '#e53935',
                        backgroundColor: 'rgba(229, 57, 53, 0.1)',
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '人数'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '月份'
                            }
                        }
                    }
                }
            });


            // 员工结构分布图表
            const structureCtx = document.getElementById('employeeStructureChart').getContext('2d');

            // 从PHP获取部门数据
            const deptData1 = <?php echo json_encode($deptData1); ?>;
            
            // 调试：在控制台输出数据，比alert更清晰
            console.log('部门数据:', deptData1);
            
            // 检查数据是否存在
            if (deptData1.labels.length > 0 && deptData1.data.length > 0) {
                new Chart(structureCtx, {
                    type: 'pie',
                    data: {
                        labels: deptData1.labels,
                        datasets: [{
                            data: deptData1.data,
                            backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                                '#FF9F40', '#8AC24A', '#607D8B', '#E91E63'
                            ],
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            } else {
                console.error('没有可用的部门数据');
                // 显示友好提示
                structureCtx.canvas.parentNode.innerHTML = '<p class="text-center text-muted">暂无部门数据</p>';
            }
        }
    </script>
</body>
</html> 