<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格统一测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-test-tube me-2"></i>
                风格统一测试
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    测试时间: <span id="test-time">2024-01-01 12:00:00</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 统计卡片测试 -->
        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-tasks stat-icon"></i>
                        <h5 class="card-title">主要指标</h5>
                        <h2 class="card-text">100</h2>
                        <p class="stat-info">测试数据</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-check-circle stat-icon"></i>
                        <h5 class="card-title">成功指标</h5>
                        <h2 class="card-text">85</h2>
                        <p class="stat-info">85% 成功率</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle stat-icon"></i>
                        <h5 class="card-title">警告指标</h5>
                        <h2 class="card-text">12</h2>
                        <p class="stat-info">需要关注</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-danger">
                    <div class="card-body">
                        <i class="fas fa-times-circle stat-icon"></i>
                        <h5 class="card-title">错误指标</h5>
                        <h2 class="card-text">3</h2>
                        <p class="stat-info">需要处理</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 卡片测试 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">测试图表</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <p class="text-center mt-5">图表容器测试</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">测试表格</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目</th>
                                        <th>状态</th>
                                        <th>进度</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>测试项目1</td>
                                        <td><span class="badge bg-success">完成</span></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 100%">100%</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>测试项目2</td>
                                        <td><span class="badge bg-warning">进行中</span></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 60%">60%</div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日期选择器测试 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="date-range-container">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" value="2024-01-01">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" value="2024-01-31">
                        <button type="button" class="btn btn-primary">查询</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
