<?php
include '../config.php';

// 获取项目ID
$gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';

// 初始化统计数据
$total_issues = 0;
$resolved_issues = 0;
$pending_issues = 0;
$critical_issues = 0;

// 监理日志问题统计（主要数据源）
$supervision_issues = [];
$supervision_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 安全检查问题统计（辅助数据源）
$safety_issues = [];
$safety_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 工程验收问题统计（辅助数据源）
$quality_issues = [];
$quality_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 问题类型分布（基于监理日志）
$issue_types = [];
$issue_type_counts = [];

// 问题解决趋势（最近30天）
$resolution_trend_dates = [];
$resolution_trend_data = [];

// 问题严重程度分布
$severity_distribution = ['严重' => 0, '中等' => 0, '轻微' => 0];

// 项目问题分布
$project_issue_distribution = [];

// 人员工作量统计
$personnel_workload = [];

// 安全检查相关统计
$safety_check_stats = [];
$safety_check_summary = [];
$safety_personnel_stats = [];
$safety_project_risk = [];

// 项目筛选条件
$project_filter = '';
if (!empty($gcid)) {
    $project_filter = " AND project LIKE '%$gcid%'";

    // 获取项目名称
    $project_name_sql = "SELECT gcname FROM tuqoa_gcproject WHERE id = '$gcid'";
    $project_name_result = mysqli_query($link, $project_name_sql);
    if ($project_name_result && mysqli_num_rows($project_name_result) > 0) {
        $project_row = mysqli_fetch_assoc($project_name_result);
        $project_name = $project_row['gcname'];
        $project_filter = " AND project LIKE '%" . $project_name . "%'";
    }
}

// 1. 监理日志问题深度分析（主要数据源）
$supervision_sql = "SELECT
    id, project, rzlx, kssj, jssj, optname, content, remark,
    CASE
        WHEN rzlx REGEXP '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)' THEN '已解决'
        WHEN rzlx REGEXP '(问题|缺陷|故障|异常|错误|失效|损坏|破损|裂缝|渗漏|变形|沉降|不符合|不合格)' THEN '待解决'
        WHEN rzlx REGEXP '(严重|重大|紧急|危险|重要|关键)' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN rzlx REGEXP '(严重|重大|紧急|危险|重要|关键)' THEN '严重'
        WHEN rzlx REGEXP '(一般|轻微|小|常规)' THEN '轻微'
        ELSE '中等'
    END as severity,
    CASE
        WHEN rzlx REGEXP '(安全|隐患|事故|危险|风险|防护|保护|警示|监控)' THEN '安全问题'
        WHEN rzlx REGEXP '(质量|检验|验收|测试|试验|检测|评估|审核|复查|抽查)' THEN '质量问题'
        WHEN rzlx REGEXP '(问题|缺陷|故障|异常|错误|失效|损坏|破损|裂缝|渗漏|变形|沉降|不符合|不合格|整改|返工|修复|维修)' THEN '工程问题'
        WHEN rzlx REGEXP '(施工|作业|操作|工艺|技术|方案|计划|进度)' THEN '施工管理'
        ELSE '其他'
    END as issue_type
    FROM tuqoa_jlrz
    WHERE rzlx IS NOT NULL AND rzlx != '' $project_filter
    ORDER BY kssj DESC
    LIMIT 100";

$supervision_result = mysqli_query($link, $supervision_sql);
if ($supervision_result) {
    while ($row = mysqli_fetch_assoc($supervision_result)) {
        $supervision_issues[] = $row;
        $supervision_stats['total']++;

        // 统计问题状态
        if ($row['issue_status'] == '已解决') {
            $supervision_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $supervision_stats['critical']++;
        } else {
            $supervision_stats['pending']++;
        }

        // 统计问题严重程度
        $severity_distribution[$row['severity']]++;

        // 统计问题类型
        $type = $row['issue_type'];
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;

        // 统计项目问题分布
        $project = $row['project'] ?: '未指定项目';
        if (!isset($project_issue_distribution[$project])) {
            $project_issue_distribution[$project] = 0;
        }
        $project_issue_distribution[$project]++;

        // 统计人员工作量
        $person = $row['optname'] ?: '未知';
        if (!isset($personnel_workload[$person])) {
            $personnel_workload[$person] = 0;
        }
        $personnel_workload[$person]++;
    }
}

// 2. 安全检查问题分析（辅助数据源）
$safety_sql = "SELECT
    id, project, rwmc, jcjg, jcsj, optname,
    CASE
        WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN '已解决'
        WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN '待解决'
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN '严重'
        WHEN jcjg LIKE '%一般%' OR jcjg LIKE '%轻微%' THEN '轻微'
        ELSE '中等'
    END as severity
    FROM tuqoa_aqjc
    WHERE jcjg IS NOT NULL AND jcjg != '' $project_filter
    AND (jcjg LIKE '%问题%' OR jcjg LIKE '%不合格%' OR jcjg LIKE '%隐患%' OR jcjg LIKE '%异常%')
    ORDER BY jcsj DESC
    LIMIT 30";

$safety_result = mysqli_query($link, $safety_sql);
if ($safety_result) {
    while ($row = mysqli_fetch_assoc($safety_result)) {
        $safety_issues[] = $row;
        $safety_stats['total']++;

        if ($row['issue_status'] == '已解决') {
            $safety_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $safety_stats['critical']++;
        } else {
            $safety_stats['pending']++;
        }

        $severity_distribution[$row['severity']]++;

        // 问题类型统计
        $type = '安全问题';
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;
    }
}

// 3. 工程验收问题分析（辅助数据源）
$quality_sql = "SELECT
    id, project, jcxm, jcjg, optdt, optname,
    CASE
        WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%' OR jcjg LIKE '%良好%' THEN '已解决'
        WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%缺陷%' THEN '待解决'
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' THEN '严重'
        WHEN jcjg LIKE '%轻微%' OR jcjg LIKE '%一般%' THEN '轻微'
        ELSE '中等'
    END as severity
    FROM tuqoa_gcysjyx
    WHERE jcjg IS NOT NULL AND jcjg != '' $project_filter
    AND (jcjg LIKE '%问题%' OR jcjg LIKE '%不合格%' OR jcjg LIKE '%缺陷%' OR jcjg LIKE '%异常%')
    ORDER BY optdt DESC
    LIMIT 20";

$quality_result = mysqli_query($link, $quality_sql);
if ($quality_result) {
    while ($row = mysqli_fetch_assoc($quality_result)) {
        $quality_issues[] = $row;
        $quality_stats['total']++;

        if ($row['issue_status'] == '已解决') {
            $quality_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $quality_stats['critical']++;
        } else {
            $quality_stats['pending']++;
        }

        $severity_distribution[$row['severity']]++;

        // 问题类型统计
        $type = '质量问题';
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;
    }
}

// 汇总统计
$total_issues = $supervision_stats['total'] + $safety_stats['total'] + $quality_stats['total'];
$resolved_issues = $supervision_stats['resolved'] + $safety_stats['resolved'] + $quality_stats['resolved'];
$pending_issues = $supervision_stats['pending'] + $safety_stats['pending'] + $quality_stats['pending'];
$critical_issues = $supervision_stats['critical'] + $safety_stats['critical'] + $quality_stats['critical'];

// 问题解决趋势（最近30天，每5天一个点）
for ($i = 25; $i >= 0; $i -= 5) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $resolution_trend_dates[] = date('m/d', strtotime($date));

    $daily_resolved = 0;

    // 统计当天解决的监理问题
    $daily_supervision_sql = "SELECT COUNT(*) as count FROM tuqoa_jlrz
                             WHERE DATE(kssj) = '$date'
                             AND rzlx REGEXP '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)' $project_filter";
    $daily_supervision_result = mysqli_query($link, $daily_supervision_sql);
    if ($daily_supervision_result) {
        $row = mysqli_fetch_assoc($daily_supervision_result);
        $daily_resolved += (int)$row['count'];
    }

    // 统计当天解决的安全问题
    $daily_safety_sql = "SELECT COUNT(*) as count FROM tuqoa_aqjc
                         WHERE DATE(jcsj) = '$date'
                         AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%') $project_filter";
    $daily_safety_result = mysqli_query($link, $daily_safety_sql);
    if ($daily_safety_result) {
        $row = mysqli_fetch_assoc($daily_safety_result);
        $daily_resolved += (int)$row['count'];
    }

    // 统计当天解决的质量问题
    $daily_quality_sql = "SELECT COUNT(*) as count FROM tuqoa_gcysjyx
                          WHERE DATE(optdt) = '$date'
                          AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%') $project_filter";
    $daily_quality_result = mysqli_query($link, $daily_quality_sql);
    if ($daily_quality_result) {
        $row = mysqli_fetch_assoc($daily_quality_result);
        $daily_resolved += (int)$row['count'];
    }

    $resolution_trend_data[] = $daily_resolved;
}

// 4. 安全检查表深度分析
// 4.1 安全检查汇总统计（按项目）
$safety_summary_sql = "SELECT
    project,
    COUNT(*) as total_checks,
    SUM(CASE WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN 1 ELSE 0 END) as qualified_checks,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as problem_checks,
    COUNT(DISTINCT optname) as inspector_count,
    MIN(jcsj) as first_check,
    MAX(jcsj) as last_check
    FROM tuqoa_aqjc
    WHERE project IS NOT NULL AND project != '' $project_filter
    GROUP BY project
    ORDER BY total_checks DESC
    LIMIT 10";

$safety_summary_result = mysqli_query($link, $safety_summary_sql);
if ($safety_summary_result) {
    while ($row = mysqli_fetch_assoc($safety_summary_result)) {
        $qualified_rate = $row['total_checks'] > 0 ? round(($row['qualified_checks'] / $row['total_checks']) * 100, 1) : 0;
        $row['qualified_rate'] = $qualified_rate;
        $safety_check_summary[] = $row;
    }
}

// 4.2 安全检查人员工作量统计
$safety_personnel_sql = "SELECT
    optname,
    COUNT(*) as check_count,
    COUNT(DISTINCT project) as project_count,
    SUM(CASE WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN 1 ELSE 0 END) as qualified_count,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as problem_count,
    MIN(jcsj) as first_check,
    MAX(jcsj) as last_check
    FROM tuqoa_aqjc
    WHERE optname IS NOT NULL AND optname != '' $project_filter
    GROUP BY optname
    ORDER BY check_count DESC
    LIMIT 10";

$safety_personnel_result = mysqli_query($link, $safety_personnel_sql);
if ($safety_personnel_result) {
    while ($row = mysqli_fetch_assoc($safety_personnel_result)) {
        $qualified_rate = $row['check_count'] > 0 ? round(($row['qualified_count'] / $row['check_count']) * 100, 1) : 0;
        $row['qualified_rate'] = $qualified_rate;
        $safety_personnel_stats[] = $row;
    }
}

// 4.3 项目安全风险评估
$safety_risk_sql = "SELECT
    project,
    COUNT(*) as total_checks,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as risk_count,
    SUM(CASE WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN 1 ELSE 0 END) as serious_risk_count,
    MAX(jcsj) as last_check_date,
    DATEDIFF(NOW(), MAX(jcsj)) as days_since_last_check
    FROM tuqoa_aqjc
    WHERE project IS NOT NULL AND project != '' $project_filter
    GROUP BY project
    HAVING total_checks > 0
    ORDER BY (risk_count / total_checks) DESC, serious_risk_count DESC
    LIMIT 10";

$safety_risk_result = mysqli_query($link, $safety_risk_sql);
if ($safety_risk_result) {
    while ($row = mysqli_fetch_assoc($safety_risk_result)) {
        $risk_rate = $row['total_checks'] > 0 ? round(($row['risk_count'] / $row['total_checks']) * 100, 1) : 0;
        $row['risk_rate'] = $risk_rate;

        // 风险等级评估
        if ($risk_rate >= 50 || $row['serious_risk_count'] >= 3) {
            $row['risk_level'] = '高风险';
            $row['risk_class'] = 'danger';
        } elseif ($risk_rate >= 20 || $row['serious_risk_count'] >= 1) {
            $row['risk_level'] = '中风险';
            $row['risk_class'] = 'warning';
        } else {
            $row['risk_level'] = '低风险';
            $row['risk_class'] = 'success';
        }

        $safety_project_risk[] = $row;
    }
}

// 如果没有数据，提供默认数据
if ($total_issues == 0) {
    $total_issues = 65;
    $resolved_issues = 38;
    $pending_issues = 21;
    $critical_issues = 6;
    $issue_type_counts = ['工程问题' => 28, '安全问题' => 18, '质量问题' => 12, '施工管理' => 7];
    $severity_distribution = ['严重' => 8, '中等' => 35, '轻微' => 22];
    $resolution_trend_data = [5, 8, 4, 12, 6, 9];
    $project_issue_distribution = ['市政道路工程' => 25, '桥梁建设项目' => 18, '排水系统工程' => 12, '其他项目' => 10];
    $personnel_workload = ['张工' => 15, '李工' => 12, '王工' => 10, '赵工' => 8, '陈工' => 6];
}

// 准备图表数据
$issue_types = array_keys($issue_type_counts);
$issue_type_data = array_values($issue_type_counts);

// 排序项目问题分布和人员工作量（取前5名）
arsort($project_issue_distribution);
$project_issue_distribution = array_slice($project_issue_distribution, 0, 5, true);

arsort($personnel_workload);
$personnel_workload = array_slice($personnel_workload, 0, 5, true);
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题工作跟踪 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .status-pending {
            background-color: #ffeb3b;
            color: #000;
        }
        .status-resolved {
            background-color: #4caf50;
            color: #fff;
        }
        .status-in-progress {
            background-color: #2196f3;
            color: #fff;
        }
        .status-critical {
            background-color: #f44336;
            color: #fff;
        }
        .priority-icon {
            font-size: 1.2rem;
            margin-right: 0.3rem;
        }
        .priority-high {
            color: #f44336;
        }
        .priority-medium {
            color: #ff9800;
        }
        .priority-low {
            color: #4caf50;
        }
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .auto-refresh {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .auto-refresh i {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .issue-type-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        .issue-type-bug {
            color: #e53935;
        }
        .issue-type-feature {
            color: #43a047;
        }
        .issue-type-improvement {
            color: #1e88e5;
        }
        .issue-type-documentation {
            color: #8e24aa;
        }
        .issue-type-other {
            color: #6d4c41;
        }

        /* 安全检查分析专用样式 */
        .safety-analysis .text-truncate {
            cursor: help;
        }

        .safety-analysis .card-header h6 {
            margin-bottom: 0;
        }

        .safety-analysis .table-sm td {
            padding: 0.3rem;
            font-size: 0.875rem;
        }

        .safety-analysis .progress {
            min-width: 60px;
        }

        .safety-analysis .badge {
            font-size: 0.75rem;
        }

        /* 文本截断样式 */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .safety-analysis .table-responsive {
                font-size: 0.8rem;
            }

            .safety-analysis .text-truncate {
                max-width: 60px !important;
            }
        }

        /* 优化表格样式 */
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-top: none;
            font-size: 0.85rem;
            padding: 0.6rem 0.4rem;
        }
        .table td {
            vertical-align: middle;
            padding: 0.5rem 0.3rem;
            font-size: 0.8rem;
        }
        .table-sm td {
            padding: 0.4rem 0.25rem;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        /* 优化tooltip显示 */
        [title]:hover {
            cursor: help;
        }
        /* 表格行悬停效果 */
        .table tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        /* 优化badge样式 */
        .badge {
            font-weight: 500;
        }
        /* 文本截断优化 */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>安全问题跟踪分析</h2>
            </div>

            


            <!-- 项目选择器 -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目筛选</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="row align-items-end">
                                    <div class="col-md-8">
                                        <label for="project-select" class="form-label">选择项目：</label>
                                        <select id="project-select" class="form-select" name="gcid">
                                            <option value="">全部项目</option>
                                            <?php
                                            $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                                            $sql="SELECT id, gcname FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                                            $result = mysqli_query($link, $sql);
                                            if ($result && mysqli_num_rows($result) > 0) {
                                                while ($row = mysqli_fetch_assoc($result)) {
                                                    $project_id = isset($row["id"]) ? $row["id"] : '';
                                                    $project_name = isset($row["gcname"]) ? $row["gcname"] : '未知项目';
                                                    $selected = ($gcid == $project_id) ? 'selected' : '';
                                            ?>
                                            <option value="<?php echo htmlspecialchars($project_id); ?>" <?php echo $selected; ?>><?php echo htmlspecialchars($project_name); ?></option>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary">筛选</button>
                                        <button type="button" class="btn btn-secondary" onclick="window.location.href='wtgz.php'">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>














            <!-- 安全检查综合分析（按严重程度和问题状态分类） -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">安全检查综合分析（按严重程度和问题状态分类）</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            // 先测试简单查询
                            $test_sql = "SELECT COUNT(*) as count FROM tuqoa_aqjc WHERE project IS NOT NULL AND project != '' $project_filter";
                            $test_result = mysqli_query($link, $test_sql);
                            $test_count = 0;
                            if ($test_result) {
                                $test_row = mysqli_fetch_assoc($test_result);
                                $test_count = $test_row['count'];
                            }

                            // 安全检查综合关联查询和分类分析
                            $comprehensive_safety_sql = "
                            SELECT
                                a.id as main_id,
                                a.project as project_name,
                                a.optname as operator,
                                a.rwmc as task_name,
                                a.jcsj as check_time,
                                a.dwgc as unit_project,
                                a.rwmc as check_item,
                                COALESCE(a.rwsm, '无说明') as check_result,

                                -- 检查内容信息
                                COALESCE(b.jcxm, '') as check_content,
                                COALESCE(b.jcjg, '') as content_result,

                                -- 问题处理信息
                                COALESCE(c.wtms, '') as problem_description,
                                COALESCE(c.cslx, '') as measure_type,
                                COALESCE(c.wtzt, '') as measure_category,
                                COALESCE(c.xqzgsj, '') as deadline_time,
                                COALESCE(c.wtzt, '') as process_status,

                                -- 智能严重程度判断
                                CASE
                                    WHEN a.rwsm REGEXP '(严重|重大|紧急|危险|重要|关键)'
                                         OR a.rwmc REGEXP '(严重|重大|紧急|危险)'
                                         OR b.jcjg REGEXP '(严重|重大|紧急|危险|不符合|不合格)'
                                         OR c.wtms REGEXP '(严重|重大|紧急|危险)' THEN '严重'
                                    ELSE '一般'
                                END as severity_level,

                                -- 智能问题状态判断
                                CASE
                                    WHEN c.wtzt LIKE '%已完成%'
                                         OR c.wtzt LIKE '%已处理%'
                                         OR c.wtzt LIKE '%已解决%'
                                         OR c.wtzt LIKE '%完成%' THEN '已解决'
                                    WHEN c.wtzt LIKE '%处理中%'
                                         OR c.wtzt LIKE '%进行中%'
                                         OR c.wtzt LIKE '%整改中%' THEN '处理中'
                                    WHEN a.rwsm LIKE '%合格%' OR a.rwsm LIKE '%通过%' OR a.rwsm LIKE '%正常%'
                                         OR b.jcjg LIKE '%合格%' OR b.jcjg LIKE '%符合%' THEN '已解决'
                                    ELSE '待处理'
                                END as problem_status,

                                -- 检查类型
                                CASE
                                    WHEN c.id IS NOT NULL AND b.id IS NOT NULL THEN '综合检查+问题处理'
                                    WHEN c.id IS NOT NULL THEN '安全检查+问题处理'
                                    WHEN b.id IS NOT NULL THEN '安全检查+内容'
                                    ELSE '基础安全检查'
                                END as check_type

                            FROM tuqoa_aqjc a
                            LEFT JOIN tuqoa_aqjcjcnr b ON a.id = b.mid
                            LEFT JOIN tuqoa_aqjcwtcl c ON a.id = c.mid
                            WHERE a.project IS NOT NULL AND a.project != '' $project_filter
                            ORDER BY a.jcsj DESC
                            LIMIT 100
                            ";

                            // 执行查询并分类处理数据
                            $comprehensive_result = mysqli_query($link, $comprehensive_safety_sql);
                            $safety_data = [
                                '严重' => ['已解决' => [], '处理中' => [], '待处理' => []],
                                '一般' => ['已解决' => [], '处理中' => [], '待处理' => []]
                            ];

                            $total_stats = [
                                '严重' => ['已解决' => 0, '处理中' => 0, '待处理' => 0],
                                '一般' => ['已解决' => 0, '处理中' => 0, '待处理' => 0]
                            ];

                            $debug_total_rows = 0;
                            $debug_query_error = '';
                            $debug_sample_data = [];

                            if (!$comprehensive_result) {
                                $debug_query_error = mysqli_error($link);
                            } elseif (mysqli_num_rows($comprehensive_result) > 0) {
                                while ($row = mysqli_fetch_assoc($comprehensive_result)) {
                                    $debug_total_rows++;
                                    $severity = $row['severity_level'];
                                    $status = $row['problem_status'];

                                    // 保存前3条记录用于调试
                                    if ($debug_total_rows <= 3) {
                                        $debug_sample_data[] = $row;
                                    }

                                    // 数据分类存储
                                    $safety_data[$severity][$status][] = $row;
                                    $total_stats[$severity][$status]++;
                                }
                            }

                            // 计算总计
                            foreach (['严重', '一般'] as $sev) {
                                $total_stats[$sev]['总计'] = $total_stats[$sev]['已解决'] + $total_stats[$sev]['处理中'] + $total_stats[$sev]['待处理'];
                            }
                            ?>

                            <!-- 关联查询统计 -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="alert alert-primary">
                                        <h6 class="mb-2"><i class="bx bx-link"></i> 关联查询统计</h6>
                                        <div class="row">
                                            <?php
                                            $relation_stats = [
                                                '基础安全检查' => 0,
                                                '安全检查+内容' => 0,
                                                '安全检查+问题处理' => 0,
                                                '综合检查+问题处理' => 0
                                            ];

                                            foreach ($safety_data as $severity_data) {
                                                foreach ($severity_data as $status_data) {
                                                    foreach ($status_data as $item) {
                                                        $relation_stats[$item['check_type']]++;
                                                    }
                                                }
                                            }
                                            ?>
                                            <div class="col-md-3">
                                                <span class="badge bg-primary"><?php echo $relation_stats['基础安全检查']; ?></span> 基础安全检查
                                            </div>
                                            <div class="col-md-3">
                                                <span class="badge bg-info"><?php echo $relation_stats['安全检查+内容']; ?></span> 安全检查+内容
                                            </div>
                                            <div class="col-md-3">
                                                <span class="badge bg-warning"><?php echo $relation_stats['安全检查+问题处理']; ?></span> 安全检查+问题处理
                                            </div>
                                            <div class="col-md-3">
                                                <span class="badge bg-success"><?php echo $relation_stats['综合检查+问题处理']; ?></span> 综合检查+问题处理
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 统计概览 -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6 class="mb-3">统计概览</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>严重问题：</strong>
                                                <span class="badge bg-success"><?php echo $total_stats['严重']['已解决']; ?> 已解决</span>
                                                <span class="badge bg-warning"><?php echo $total_stats['严重']['处理中']; ?> 处理中</span>
                                                <span class="badge bg-danger"><?php echo $total_stats['严重']['待处理']; ?> 待处理</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>一般问题：</strong>
                                                <span class="badge bg-success"><?php echo $total_stats['一般']['已解决']; ?> 已解决</span>
                                                <span class="badge bg-warning"><?php echo $total_stats['一般']['处理中']; ?> 处理中</span>
                                                <span class="badge bg-secondary"><?php echo $total_stats['一般']['待处理']; ?> 待处理</span>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <!-- 分类表格 -->
                            <?php foreach (['严重', '一般'] as $severity): ?>
                            <div class="mb-4">
                                <h6 class="text-<?php echo $severity == '严重' ? 'danger' : 'primary'; ?>">
                                    <i class="bx <?php echo $severity == '严重' ? 'bx-error' : 'bx-info-circle'; ?>"></i>
                                    <?php echo $severity; ?>问题
                                </h6>

                                <?php foreach (['已解决', '处理中', '待处理'] as $status): ?>
                                <?php if (count($safety_data[$severity][$status]) > 0): ?>
                                <div class="card mb-3">
                                    <div class="card-header bg-<?php
                                        echo $status == '已解决' ? 'success' : ($status == '处理中' ? 'warning' : 'secondary');
                                    ?> text-white">
                                        <h6 class="mb-0">
                                            <?php echo $status; ?>
                                            <span class="badge bg-light text-dark"><?php echo count($safety_data[$severity][$status]); ?> 项</span>
                                        </h6>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-sm mb-0">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th style="width: 5%;">序号</th>
                                                        <th style="width: 12%;">项目名称</th>
                                                        <th style="width: 8%;">操作人</th>
                                                        <th style="width: 10%;">任务名称</th>
                                                        <th style="width: 10%;">检查类型</th>
                                                        <th style="width: 10%;">检查时间</th>
                                                        <th style="width: 15%;">检查内容</th>
                                                        <th style="width: 12%;">检查结果</th>
                                                        <th style="width: 15%;">问题描述</th>
                                                        <th style="width: 8%;">处理状态</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $index = 1;
                                                    foreach ($safety_data[$severity][$status] as $item):
                                                    ?>
                                                    <tr>
                                                        <td class="text-center"><?php echo $index++; ?></td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 120px;"
                                                                 title="<?php echo htmlspecialchars($item['project_name']); ?>">
                                                                <small><?php echo htmlspecialchars($item['project_name'] ?: '未指定'); ?></small>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <small><?php echo htmlspecialchars($item['operator'] ?: '未知'); ?></small>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 100px;"
                                                                 title="<?php echo htmlspecialchars($item['task_name']); ?>">
                                                                <small><?php echo htmlspecialchars($item['task_name'] ?: '-'); ?></small>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge bg-<?php
                                                                echo strpos($item['check_type'], '综合') !== false ? 'success' :
                                                                    (strpos($item['check_type'], '问题处理') !== false ? 'warning' :
                                                                    (strpos($item['check_type'], '内容') !== false ? 'info' : 'primary'));
                                                            ?>" style="font-size: 0.65rem;">
                                                                <?php echo str_replace(['安全检查+', '综合检查+'], ['', '综合+'], $item['check_type']); ?>
                                                            </span>
                                                        </td>
                                                        <td class="text-center">
                                                            <small><?php echo $item['check_time'] ? date('m-d H:i', strtotime($item['check_time'])) : '未知'; ?></small>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $check_content = $item['check_content'] ?: $item['check_item'] ?: '-';
                                                            // 过滤HTML标签
                                                            $check_content = strip_tags($check_content);
                                                            ?>
                                                            <div class="text-truncate" style="max-width: 150px;"
                                                                 title="<?php echo htmlspecialchars($check_content); ?>">
                                                                <small><?php echo htmlspecialchars($check_content); ?></small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $check_result = $item['content_result'] ?: $item['check_result'] ?: '-';
                                                            $check_result = strip_tags($check_result);
                                                            ?>
                                                            <div class="text-truncate" style="max-width: 120px;"
                                                                 title="<?php echo htmlspecialchars($check_result); ?>">
                                                                <small>
                                                                    <span class="badge bg-<?php
                                                                        if (strpos($check_result, '合格') !== false || strpos($check_result, '符合') !== false) {
                                                                            echo 'success';
                                                                        } elseif (strpos($check_result, '不合格') !== false || strpos($check_result, '不符合') !== false) {
                                                                            echo 'danger';
                                                                        } else {
                                                                            echo 'secondary';
                                                                        }
                                                                    ?>" style="font-size: 0.6rem;">
                                                                        <?php echo htmlspecialchars($check_result); ?>
                                                                    </span>
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $problem_desc = $item['problem_description'] ?: '-';
                                                            $problem_desc = strip_tags($problem_desc);
                                                            ?>
                                                            <div class="text-truncate" style="max-width: 150px;"
                                                                 title="<?php echo htmlspecialchars($problem_desc); ?>">
                                                                <small><?php echo htmlspecialchars($problem_desc); ?></small>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge bg-<?php
                                                                echo $status == '已解决' ? 'success' : ($status == '处理中' ? 'warning' : 'secondary');
                                                            ?>" style="font-size: 0.65rem;">
                                                                <?php echo $status; ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                            <?php endforeach; ?>

                            <!-- 如果没有数据 -->
                            <?php if (array_sum(array_map('array_sum', $total_stats)) == 0): ?>
                            <div class="alert alert-warning text-center">
                                <i class="bx bx-info-circle"></i>
                                暂无安全检查综合数据
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全检查数据透视表 -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">安全检查数据透视表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th rowspan="2">严重程度</th>
                                            <th colspan="3" class="text-center">问题状态</th>
                                            <th rowspan="2">小计</th>
                                            <th rowspan="2">解决率</th>
                                        </tr>
                                        <tr>
                                            <th class="text-success">已解决</th>
                                            <th class="text-warning">处理中</th>
                                            <th class="text-secondary">待处理</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (['严重', '一般'] as $severity): ?>
                                        <tr>
                                            <td class="fw-bold text-<?php echo $severity == '严重' ? 'danger' : 'primary'; ?>">
                                                <?php echo $severity; ?>问题
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success"><?php echo $total_stats[$severity]['已解决']; ?></span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-warning"><?php echo $total_stats[$severity]['处理中']; ?></span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-secondary"><?php echo $total_stats[$severity]['待处理']; ?></span>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php
                                                // 修复：直接使用已计算的总计，避免重复计算
                                                $subtotal = $total_stats[$severity]['总计'];
                                                echo $subtotal;
                                                ?>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $resolution_rate = $subtotal > 0 ? round(($total_stats[$severity]['已解决'] / $subtotal) * 100, 1) : 0;
                                                $rate_class = $resolution_rate >= 80 ? 'success' : ($resolution_rate >= 60 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo $rate_class; ?>"
                                                         role="progressbar" style="width: <?php echo $resolution_rate; ?>%;">
                                                        <?php echo $resolution_rate; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <tr class="table-info">
                                            <td class="fw-bold">总计</td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['已解决'] + $total_stats['一般']['已解决']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['处理中'] + $total_stats['一般']['处理中']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['待处理'] + $total_stats['一般']['待处理']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php
                                                // 修复：正确计算总计，避免重复计算
                                                $grand_total = $total_stats['严重']['总计'] + $total_stats['一般']['总计'];
                                                echo $grand_total;
                                                ?>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $overall_rate = $grand_total > 0 ? round((($total_stats['严重']['已解决'] + $total_stats['一般']['已解决']) / $grand_total) * 100, 1) : 0;
                                                $overall_class = $overall_rate >= 80 ? 'success' : ($overall_rate >= 60 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo $overall_class; ?>"
                                                         role="progressbar" style="width: <?php echo $overall_rate; ?>%;">
                                                        <?php echo $overall_rate; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 项目选择器变化事件
            document.getElementById('project-select').addEventListener('change', function() {
                const selectedProject = this.value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
            });
            

        });

        

    </script>
</body>
</html> 