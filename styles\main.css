/* 统一风格样式文件 - 完全基于ssjdgzhz.php */
/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

/* 导航栏样式 - 完全匹配ssjdgzhz.php */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
}

.navbar-text {
    color: white !important;
}

/* 主内容区域 */
.main-content {
    padding: 20px;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

.container-fluid {
    padding: 20px;
}

/* 卡片样式 - 完全匹配ssjdgzhz.php */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0,123,255,0.2);
    transform: translateY(-2px);
}

/* 图表容器 - 完全匹配ssjdgzhz.php */
.chart-container {
    height: 300px;
    position: relative;
    margin: 1rem 0;
}

/* 主题色背景的图表标题 - 完全匹配ssjdgzhz.php */
.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border-bottom: none !important;
    color: white !important;
    border-radius: 0.375rem 0.375rem 0 0 !important;
    padding: 1rem 1.25rem;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.card:hover .card-header::before {
    left: 100%;
}

.card-title {
    color: white !important;
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 日期选择器样式 */
.date-range-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.date-range-container label {
    margin-right: 10px;
    font-weight: 600;
    color: var(--primary-color);
}

.date-range-container input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-right: 15px;
    font-family: inherit;
}

.date-range-container button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.date-range-container button:hover {
    background-color: var(--secondary-color);
}

/* 筛选器样式 */
.filter-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    background-color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.filter-item {
    display: flex;
    align-items: center;
}

.filter-item label {
    margin-right: 10px;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
}

.filter-item .form-select {
    min-width: 150px;
}

.filter-item .btn {
    margin-left: 5px;
}

/* 卡片样式 */
.card {
    margin-bottom: 20px;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background-color: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-title {
    margin: 0;
    font-weight: 600;
    color: var(--primary-color);
}

.card-body {
    padding: 20px;
    color: var(--dark-color);
}

/* 状态卡片 */
.status-card {
    border-left: 4px solid;
    overflow: hidden;
}

.status-card.online, .status-card.valid, .status-card.received {
    border-left-color: var(--success-color);
}

.status-card.offline, .status-card.expired, .status-card.pending {
    border-left-color: var(--danger-color);
}

.status-card.business, .status-card.warning, .status-card.expected {
    border-left-color: var(--warning-color);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 表格样式 */
.table-responsive {
    margin-top: 10px;
}

.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: var(--primary-color);
    border-top: none;
    background-color: rgba(30, 136, 229, 0.1);
}

.table td {
    vertical-align: middle;
}

/* 徽章样式 */
.badge {
    padding: 5px 10px;
    font-weight: 500;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

/* 进度条样式 */
.progress {
    height: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 5px;
    background-color: var(--primary-color);
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .main-content {
        padding: var(--container-padding);
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 992px) {
    .main-content {
        padding: calc(20px * 0.8);
    }

    .card {
        margin-bottom: 15px;
    }

    .chart-container {
        height: 200px;
    }
}

/* 统计卡片主题色样式 */
.stat-card {
    color: white !important;
    border: none;
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-card-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stat-card-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-card-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.stat-card-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.stat-card-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
}

.stat-card-purple {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
}

.stat-card-teal {
    background: linear-gradient(135deg, #20c997, #1aa179);
}

.stat-card .card-title {
    color: rgba(255,255,255,0.9) !important;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.stat-card .card-text {
    color: white !important;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.stat-card .stat-info {
    color: rgba(255,255,255,0.8) !important;
    font-size: 0.85rem;
    margin-bottom: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.stat-card .stat-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 2rem;
    opacity: 0.3;
}

/* 表格样式优化 - 完全匹配ssjdgzhz.php */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
    line-height: 1.4;
}

.table td small {
    font-size: 0.7rem;
    opacity: 0.8;
}

.table .text-danger {
    font-weight: 500;
}

.table .text-success {
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.15s ease-in-out;
}

/* 新增业务表格样式 - 来自ssjdgzhz.php */
.project-table .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.approval-table .table th {
    background-color: #fff3cd;
    border-top: none;
    font-weight: 600;
    color: #856404;
}

.table-responsive {
    border-radius: 0.375rem;
}

.progress {
    height: 8px;
}

.badge {
    font-size: 0.75rem;
}

/* 按钮组样式 - 来自ssjdgzhz.php */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 状态徽章样式 - 来自ssjdgzhz.php */
.status-badge {
    font-size: 0.8rem;
    padding: 0.35rem 0.65rem;
}

/* 数据卡片增强 - 来自ssjdgzhz.php */
.data-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.data-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 表格行悬停效果 */
.table tbody tr[data-type] td:first-child {
    border-left: 3px solid transparent;
}

.table tbody tr:has(.text-danger) td:first-child {
    border-left-color: #dc3545;
}

.table tbody tr:has(.text-success) td:first-child {
    border-left-color: #28a745;
}

/* 工作状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-completed {
    background-color: #28a745;
}

.status-pending {
    background-color: #ffc107;
}

.status-overdue {
    background-color: #dc3545;
}

/* 进度条样式 */
.progress {
    height: 20px;
    border-radius: 5px;
}

.progress-bar {
    border-radius: 5px;
    background-color: #007bff;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.35rem 0.65rem;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
}

/* 数据卡片增强 */
.data-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.data-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 表格响应式 */
.table-responsive {
    border-radius: 0.375rem;
}

/* 工作类型图标样式 */
.work-type-icon {
    margin-right: 8px;
    font-size: 1.2rem;
}

.work-type-inspection {
    color: #1e88e5;
}

.work-type-safety {
    color: #e53935;
}

.work-type-supervision {
    color: #43a047;
}

.work-type-acceptance {
    color: #fb8c00;
}

.work-type-testing {
    color: #8e24aa;
}

.work-type-sampling {
    color: #00acc1;
}

.work-type-meeting {
    color: #6d4c41;
}

.work-type-default {
    color: #607d8b;
}

/* 响应式优化 */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 20px;
    }

    .chart-container {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }

    .stat-card .card-text {
        font-size: 2rem;
    }

    .container-fluid {
        padding: 15px;
    }
}

/* 特殊业务表格样式 */
.project-table .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.approval-table .table th {
    background-color: #fff3cd;
    border-top: none;
    font-weight: 600;
    color: #856404;
}

/* 日期选择器样式优化 */
.date-range-container {
    background-color: white;
    padding: 15px;
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.date-range-container .form-group {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.date-range-container label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.date-range-container input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    font-family: inherit;
}

.date-range-container button {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.date-range-container button:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

@media (max-width: 768px) {
    .main-content {
        padding: calc(var(--container-padding) * 0.6);
    }
    
    .date-range-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-range-container label {
        margin-bottom: 10px;
    }
    
    .date-range-container input[type="date"] {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .date-range-container button {
        width: 100%;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-item {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .filter-item .form-select {
        width: 100%;
    }
    
    .table-responsive {
        margin: 0 -15px;
        width: calc(100% + 30px);
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: calc(var(--container-padding) * 0.4);
    }
    
    .card-header {
        padding: 10px 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .chart-container {
        height: 180px;
    }
    
    .table th, .table td {
        padding: 8px;
        font-size: 14px;
    }
}

/* 打印样式优化 */
@media print {
    .main-content {
        width: 100%;
        max-width: none;
        padding: 0;
    }
    
    .card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 页面标题样式 */
h2.mb-4 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* 卡片标题样式 */
.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

.card-header .card-title {
    color: white;
}

/* 数值样式 */
h2.card-text {
    color: var(--primary-color);
    font-weight: bold;
}

.card-body .card-text {
    color: var(--dark-color);
}

/* 趋势指标样式 */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-dark {
    color: var(--dark-color) !important;
}

.text-light {
    color: var(--light-color) !important;
}

.text-gray {
    color: var(--gray-color) !important;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link {
    color: var(--gray-color);
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* 模态框样式 */
.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.modal-footer {
    border-top: none;
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* 表单样式 */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(30, 136, 229, 0.25);
}

/* 刷新时间样式 */
.refresh-time {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-left: 10px;
}

/* 自动刷新开关样式 */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 项目详情样式 */
.project-details {
    margin-top: 20px;
}

.project-details .card {
    margin-bottom: 15px;
}

/* 工作记录样式 */
.work-record {
    margin-top: 10px;
}

.work-record .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 问题跟踪样式 */
.issue-tracking .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 成本核算样式 */
.cost-accounting .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 合同支付样式 */
.contract-payment .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 部门汇总样式 */
.department-summary .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 公司汇总样式 */
.company-summary .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 月度业务样式 */
.monthly-business .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 员工动态样式 */
.employee-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 证书动态样式 */
.certificate-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 支付统计样式 */
.payment-statistics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 业务统计样式 */
.business-statistics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 业务动态样式 */
.business-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
} 