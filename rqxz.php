<?php
// 获取本月的第一天和最后一天
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');

// 初始化日期变量
$startDate = isset($_POST['start_date']) ? $_POST['start_date'] : $firstDayOfMonth;
$endDate = isset($_POST['end_date']) ? $_POST['end_date'] : $lastDayOfMonth;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期区间选择 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <style>
        /* 页面特定样式 */
        .result {
            background-color: #e9f7ef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>日期区间选择</h1>
    
    <div class="date-form">
        <form method="post" action="">
            <div class="form-group">
                <label for="start_date">开始日期:</label>
                <input type="date" id="start_date" name="start_date" 
                       value="<?php echo htmlspecialchars($startDate); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="end_date">结束日期:</label>
                <input type="date" id="end_date" name="end_date" 
                       value="<?php echo htmlspecialchars($endDate); ?>" required>
            </div>
            
            <button type="submit">提交</button>
        </form>
    </div>
    
    <?php
    // 检查是否提交了表单
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
        if (strtotime($startDate) > strtotime($endDate)) {
            echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
        } else {
            // 格式化日期用于显示
            $displayStart = date('Y年m月d日', strtotime($startDate));
            $displayEnd = date('Y年m月d日', strtotime($endDate));
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
            
            echo '<div class="result">';
            echo "<h3>您选择的日期区间：</h3>";
            echo "<p>开始日期：{$displayStart}</p>";
            echo "<p>结束日期：{$displayEnd}</p>";
            echo "<p>共选择：{$daysDiff} 天</p>";
            
            // 添加本月信息
            $currentMonth = date('Y年m月');
            $monthDays = date('t', strtotime($firstDayOfMonth));
            echo "<h3>本月信息：</h3>";
            echo "<p>当前月份：{$currentMonth}</p>";
            echo "<p>本月天数：{$monthDays} 天</p>";
            echo '</div>';
        }
    }else{
        echo("获取时间方法");
    }
    ?>
</body>
</html>