<?php
// 启用错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!isset($link) || !$link) {
    die("数据库连接失败");
}

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}

// 初始化所有需要的变量
$fwfhj = 0;
$cz = 0;
$zgrs = 1;
$rjcz = 0;
$month = [];
$fwfhj_chart = [];
$ysjehj = [];
$total_wccz = [];

// 计算服务费合计
$sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $fwfhj = $row["fwfhj"] ? $row["fwfhj"] : 0;
    }
} else {
    error_log("数据库查询错误: " . mysqli_error($link));
}

// 计算总产值
$sql="SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $cz = $row["hj"] ? $row["hj"] : 0;
    }
} else {
    error_log("数据库查询错误: " . mysqli_error($link));
}

// 计算员工人数
$sql="SELECT count(*) hj FROM `tuqoa_rydp` WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $zgrs = $row["hj"] > 0 ? $row["hj"] : 1; // 避免除零错误
    }
} else {
    error_log("数据库查询错误: " . mysqli_error($link));
}
$rjcz = number_format($cz / $zgrs, 4);

// 生成最近6个月的数据，不使用CTE以兼容更多MySQL版本
for ($i = 5; $i >= 0; $i--) {
    $monthStr = date('Y-m', strtotime("-$i months"));
    $month[] = date('n', strtotime("-$i months")) . "月";

    // 查询该月的服务费合计
    $sql = "SELECT IFNULL(SUM(fwf), 0) AS fwfhj FROM `tuqoa_htgl` WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$monthStr'";
    $result = mysqli_query($link, $sql);
    $monthlyFwf = 0;
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $monthlyFwf = (float)$row["fwfhj"];
    } else {
        error_log("数据库查询错误: " . mysqli_error($link));
    }
    $fwfhj_chart[] = $monthlyFwf;
}

// 生成最近6个月的到账额数据
for ($i = 5; $i >= 0; $i--) {
    $monthStr = date('Y-m', strtotime("-$i months"));

    // 查询该月的到账额合计
    $sql = "SELECT IFNULL(SUM(ysje), 0) AS ysjehj FROM `tuqoa_htsf` WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'";
    $result = mysqli_query($link, $sql);
    $monthlyYsje = 0;
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $monthlyYsje = (float)$row["ysjehj"];
    } else {
        error_log("数据库查询错误: " . mysqli_error($link));
    }
    $ysjehj[] = $monthlyYsje;
}

// 生成最近6个月的产值数据
for ($i = 5; $i >= 0; $i--) {
    $monthStr = date('Y-m', strtotime("-$i months"));

    // 查询该月的产值合计
    $sql = "SELECT IFNULL(SUM(wccz), 0) AS total_wccz FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$monthStr'";
    $result = mysqli_query($link, $sql);
    $monthlyWccz = 0;
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $monthlyWccz = (float)$row["total_wccz"];
    } else {
        error_log("数据库查询错误: " . mysqli_error($link));
    }
    $total_wccz[] = $monthlyWccz;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据分析 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-trending-up me-2"></i>
                月度经营数据分析
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">
                        <i class="bx bx-calendar me-1"></i>开始日期:
                    </label>
                    <input type="date" id="start-date" name="start-date"
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">
                        <i class="bx bx-calendar me-1"></i>结束日期:
                    </label>
                    <input type="date" id="end-date" name="end-date"
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>
            
        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-contract stat-icon"></i>
                        <h5 class="card-title">本月新接合同额</h5>
                        <h2 class="card-text">¥<?php echo number_format($fwfhj, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <h5 class="card-title">总产值</h5>
                        <h2 class="card-text">¥<?php echo number_format($cz, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-user-tie stat-icon"></i>
                        <h5 class="card-title">人均产值</h5>
                        <h2 class="card-text">¥<?php echo number_format($rjcz, 1); ?></h2>
                        <p class="stat-info">万元/人</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card stat-card stat-card-info">
                    <div class="card-body">
                        <i class="fas fa-percentage stat-icon"></i>
                        <h5 class="card-title">本月成本占比</h5>
                        <h2 class="card-text">65%</h2>
                        <p class="stat-info">成本控制率</p>
                    </div>
                </div>
            </div>
        </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同额与到账额趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">实际成本与预计成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costSalaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度经营数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>月份</th>
                                            <th>项目总投资</th>
                                            <th>服务费合计</th>
                                            <th>预计收款</th>
                                            <th>到账</th>
                                            <th>差额</th>
                                            <th>月人数</th>
                                            <th>工资汇总</th>
                                            <th>工资占比</th>
                                            <th>成本汇总</th>
                                            <th>成本占比</th>
                                            <th>人均产值</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 使用简化的查询，按月份分组显示数据
                                        $sql="SELECT DATE_FORMAT(qdsj, '%Y-%m') AS month,
                                                     COALESCE(SUM(ztz), 0) AS ztzhj,
                                                     COALESCE(SUM(fwf), 0) AS fwfhj
                                              FROM `tuqoa_htgl`
                                              WHERE `qdsj` >= '$startDate' AND `qdsj` <= '$endDate'
                                              GROUP BY DATE_FORMAT(qdsj, '%Y-%m')
                                              ORDER BY month";

                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                // 获取基本数据
                                                $合同签订月份 = $row["month"];
                                                $总投资合计 = $row["ztzhj"];
                                                $服务费合计 = $row["fwfhj"];

                                                // 初始化所有变量
                                                $应回收款 = 0;
                                                $已收回款 = 0;
                                                $差额 = 0;
                                                $月人数 = 0;
                                                $应发工资合计 = 0;
                                                $预算成本费用 = 0;
                                                $预算直接费 = 0;
                                                $企业管理费 = 0;
                                                $经营业务费 = 0;
                                                $员工社保等上缴金额合计 = 0;
                                                $实际成本费用 = 0;
                                                $实际直接费 = 0;
                                                $完成产值 = 0;
                                                $人均产值 = 0;

                                                // 查询收款数据
                                                $sql1 = "SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as ysjehj FROM `tuqoa_htsf` WHERE `yjsj` like '".$合同签订月份."%'";
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    $应回收款 = $row1["yjjehj"];
                                                    $已收回款 = $row1["ysjehj"];
                                                    $差额 = $应回收款 - $已收回款;
                                                }

                                                // 查询工资数据
                                                $sql1 = "SELECT count(*) yrs,sum(sfgz) sfgzhj FROM `tuqoa_hrsalary` WHERE `month`='".$合同签订月份."'";
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    $月人数 = $row1["yrs"];
                                                    $应发工资合计 = $row1["sfgzhj"];
                                                }

                                                // 查询成本数据
                                                $sql1 = "SELECT * FROM `tuqoa_xmhstjzl` WHERE `sbrq` like '".$合同签订月份."%'";
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    if ($row1) {
                                                        $预算成本费用 = $row1["yszcbfy"];
                                                        $预算直接费 = $row1["yszjf"];
                                                        $企业管理费 = $row1["qyglf"];
                                                        $经营业务费 = $row1["jyywf"];
                                                    }
                                                }

                                                // 计算实际成本
                                                $实际成本费用 = $应发工资合计 + $员工社保等上缴金额合计 + $企业管理费 + $经营业务费;
                                                $实际直接费 = $应发工资合计 + $员工社保等上缴金额合计;

                                                // 查询产值数据
                                                $sql1 = "SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb` WHERE `sbrq` like '".$合同签订月份."%'";
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    $完成产值 = $row1["hj"];
                                                }

                                                // 计算人均产值
                                                if ($月人数 > 0) {
                                                    $人均产值 = number_format($完成产值 / $月人数, 4);
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo $合同签订月份; ?></td>
                                            <td><?php echo $总投资合计; ?>万</td>
                                            <td><?php echo $服务费合计; ?>万</td>
                                            <td>¥<?php echo $应回收款; ?>万</td>
                                            <td>¥<?php echo $已收回款; ?>万</td>
                                            <td>¥<?php echo $差额; ?>万</td>
                                            <td><?php echo $月人数; ?>人</td>
                                            <td><?php echo $应发工资合计; ?>元</td>
                                            <td>15%</td>
                                            <td>¥<?php echo $实际成本费用; ?></td>
                                            <td>65%</td>
                                            <td><?php echo $人均产值; ?>万</td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='12'>数据库查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月产值趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="perCapitaChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">尾款趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="tailPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);
            

            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 合同额与到账额趋势图表
            const contractPaymentCtx = document.getElementById('contractPaymentChart').getContext('2d');
            new Chart(contractPaymentCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '新接合同额',
                        data: <?php echo json_encode($fwfhj_chart); ?>,
                        borderColor: '#1e88e5',
                        backgroundColor: 'rgba(30, 136, 229, 0.1)',
                        tension: 0.1,
                        fill: true
                    }, {
                        label: '到账额',
                        data: <?php echo json_encode($ysjehj); ?>,
                        borderColor: '#43a047',
                        backgroundColor: 'rgba(67, 160, 71, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 产值趋势图表
            const costSalaryCtx = document.getElementById('costSalaryChart').getContext('2d');
            new Chart(costSalaryCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '完成产值',
                        data: <?php echo json_encode($total_wccz); ?>,
                        borderColor: '#e53935',
                        backgroundColor: 'rgba(229, 57, 53, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '占比（%）'
                            }
                        }
                    }
                }
            });

            // 人均产值趋势图表
            const perCapitaCtx = document.getElementById('perCapitaChart').getContext('2d');
            //const labels = <?php echo json_encode($month); ?>;
            
            
            //alert(chartData);
            new Chart(perCapitaCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '月产值合计',
                        data: <?php echo json_encode($total_wccz); ?>,
                        borderColor: '#8e24aa',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 尾款趋势图表
            const tailPaymentCtx = document.getElementById('tailPaymentChart').getContext('2d');
            new Chart(tailPaymentCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '尾款',
                        data: [100, 150, 200, 180, 220, 200],
                        borderColor: '#fb8c00',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 每3秒更新一次时间
        setInterval(updateTime, 3000);
    </script>
</body>
</html>