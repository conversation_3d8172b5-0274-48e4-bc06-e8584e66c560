// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 添加动画效果
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // 设置当前页面的导航链接为激活状态
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });

    // 响应式侧边栏切换
    const toggleSidebar = document.getElementById('toggle-sidebar');
    if (toggleSidebar) {
        toggleSidebar.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
            document.querySelector('.main-content').classList.toggle('expanded');
        });
    }

    // 图表初始化（如果页面中有图表）
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }
});

// 初始化图表
function initializeCharts() {
    // 这里可以放置所有图表的初始化代码
    // 由于每个页面的图表不同，这里只是一个示例
    console.log('Charts initialized');
}

// 格式化数字为货币格式
function formatCurrency(value) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
}

// 格式化日期
function formatDate(dateString) {
    const options = { year: 'numeric', month: '2-digit', day: '2-digit' };
    return new Date(dateString).toLocaleDateString('zh-CN', options);
}

// 计算百分比变化
function calculatePercentageChange(current, previous) {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
}

// 获取状态标签
function getStatusBadge(status) {
    let badgeClass = '';
    let badgeText = '';
    
    switch(status) {
        case 'active':
        case 'valid':
        case 'received':
            badgeClass = 'bg-success';
            badgeText = '有效';
            break;
        case 'pending':
        case 'warning':
        case 'expected':
            badgeClass = 'bg-warning';
            badgeText = '待处理';
            break;
        case 'inactive':
        case 'expired':
        case 'pending':
            badgeClass = 'bg-danger';
            badgeText = '已过期';
            break;
        default:
            badgeClass = 'bg-secondary';
            badgeText = status;
    }
    
    return `<span class="badge ${badgeClass}">${badgeText}</span>`;
} 