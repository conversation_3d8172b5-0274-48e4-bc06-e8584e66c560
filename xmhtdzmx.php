<?php
include '../config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目合同到账明细 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 400px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-receipt me-2"></i>
                项目合同到账明细
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 项目选择器 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="project-select">选择项目：</label>
                    <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                        <?php
                        $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '0';
                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                        if ($result) {
                            while ($row = mysqli_fetch_assoc($result)) {
                                $selected = ($gcid == $row["id"]) ? 'selected' : '';
                        ?>
                        <option value="<?php echo $row["id"]?>" <?php echo $selected?>><?php echo $row["gcname"]?></option>
                        <?php
                            }
                        }
                        ?>
                    </select>
                </div>
                <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                
            </div>
            </form>

            <?php
            // 查询选中项目的基本信息
            $project_info = [];
            $contract_total = 0;
            $received_total = 0;
            $management_fee = 0;
            $investment_amount = 0;

            if ($gcid > 0) {
                // 查询项目基本信息
                $sql = "SELECT * FROM `tuqoa_gcproject` WHERE `id` = $gcid";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    $project_info = mysqli_fetch_assoc($result);
                    $investment_amount = isset($project_info['zaojia']) ? $project_info['zaojia'] : 0;
                }

                // 查询合同总额
                $sql = "SELECT IFNULL(SUM(fwf), 0) as total FROM `tuqoa_htgl` WHERE `projectid` = $gcid";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $contract_total = $row['total'];
                }

                // 查询已收款总额
                $sql = "SELECT IFNULL(SUM(ysje), 0) as total FROM `tuqoa_htsf` WHERE `projectid` = $gcid";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $received_total = $row['total'];
                }

                // 计算管理费（假设为合同总额的10%）
                $management_fee = $contract_total * 0.1;
            }

            // 计算收款率
            $payment_rate = $contract_total > 0 ? round(($received_total / $contract_total) * 100, 1) : 0;

            // 查询图表数据
            $chart_data = [];

            if ($gcid > 0) {
                // 1. 合同到账趋势数据（按月统计）
                $trend_months = [];
                $trend_planned = [];
                $trend_actual = [];

                for ($i = 1; $i <= 12; $i++) {
                    $month = sprintf('%02d', $i);
                    $trend_months[] = $month . '月';

                    // 查询当月预计收款
                    $sql = "SELECT IFNULL(SUM(yjje), 0) as amount FROM `tuqoa_htsf`
                           WHERE `projectid` = $gcid AND DATE_FORMAT(yjsj, '%m') = '$month'";
                    $result = mysqli_query($link, $sql);
                    $planned = $result ? mysqli_fetch_assoc($result)['amount'] : 0;
                    $trend_planned[] = (float)$planned;

                    // 查询当月实际收款
                    $sql = "SELECT IFNULL(SUM(ysje), 0) as amount FROM `tuqoa_htsf`
                           WHERE `projectid` = $gcid AND DATE_FORMAT(sksj, '%m') = '$month'";
                    $result = mysqli_query($link, $sql);
                    $actual = $result ? mysqli_fetch_assoc($result)['amount'] : 0;
                    $trend_actual[] = (float)$actual;
                }

                // 2. 收款完成率分析数据
                $completion_labels = [];
                $completion_data = [];
                $sql = "SELECT
                    CASE
                        WHEN ysje = 0 THEN '未收款'
                        WHEN ysje >= yjje THEN '完全收款'
                        WHEN ysje > 0 AND ysje < yjje THEN '部分收款'
                        ELSE '其他'
                    END as completion_status,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `projectid` = $gcid AND yjje > 0
                GROUP BY completion_status";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $completion_labels[] = $row['completion_status'];
                        $completion_data[] = (int)$row['count'];
                    }
                }

                if (empty($completion_labels)) {
                    $completion_labels = ['未收款', '部分收款', '完全收款'];
                    $completion_data = [1, 1, 1];
                }

                // 3. 收款时效分析数据
                $timing_labels = [];
                $timing_data = [];
                $sql = "SELECT
                    CASE
                        WHEN sksj IS NULL OR sksj = '0000-00-00' THEN '未收款'
                        WHEN DATEDIFF(sksj, yjsj) <= 0 THEN '提前收款'
                        WHEN DATEDIFF(sksj, yjsj) <= 30 THEN '按时收款'
                        WHEN DATEDIFF(sksj, yjsj) <= 90 THEN '延迟收款'
                        ELSE '严重延迟'
                    END as timing_status,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `projectid` = $gcid AND yjje > 0
                GROUP BY timing_status";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $timing_labels[] = $row['timing_status'];
                        $timing_data[] = (int)$row['count'];
                    }
                }

                if (empty($timing_labels)) {
                    $timing_labels = ['未收款', '按时收款', '延迟收款'];
                    $timing_data = [1, 1, 1];
                }

                // 4. 收款进度数据
                $progress_labels = ['已收款', '未收款'];
                $progress_data = [$received_total, max(0, $contract_total - $received_total)];

                // 5. 合同收款状态分布
                $status_labels = [];
                $status_data = [];
                $sql = "SELECT
                    CASE
                        WHEN sfjs = '是' THEN '已结算'
                        WHEN ysje >= yjje THEN '收款完成'
                        WHEN ysje > 0 THEN '部分收款'
                        ELSE '未收款'
                    END as payment_status,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `projectid` = $gcid
                GROUP BY payment_status";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $status_labels[] = $row['payment_status'];
                        $status_data[] = (int)$row['count'];
                    }
                }

                if (empty($status_labels)) {
                    $status_labels = ['未收款', '部分收款', '收款完成'];
                    $status_data = [1, 1, 1];
                }

                // 6. 收款金额区间分布
                $range_labels = [];
                $range_data = [];
                $sql = "SELECT
                    CASE
                        WHEN ysje = 0 THEN '0万'
                        WHEN ysje <= 50 THEN '1-50万'
                        WHEN ysje <= 100 THEN '51-100万'
                        WHEN ysje <= 200 THEN '101-200万'
                        WHEN ysje <= 500 THEN '201-500万'
                        ELSE '500万以上'
                    END as amount_range,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `projectid` = $gcid
                GROUP BY amount_range
                ORDER BY
                    CASE
                        WHEN ysje = 0 THEN 1
                        WHEN ysje <= 50 THEN 2
                        WHEN ysje <= 100 THEN 3
                        WHEN ysje <= 200 THEN 4
                        WHEN ysje <= 500 THEN 5
                        ELSE 6
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $range_labels[] = $row['amount_range'];
                        $range_data[] = (int)$row['count'];
                    }
                }

                if (empty($range_labels)) {
                    $range_labels = ['0万', '1-50万', '51-100万'];
                    $range_data = [1, 1, 1];
                }

                $chart_data = [
                    'trend' => [
                        'months' => $trend_months,
                        'planned' => $trend_planned,
                        'actual' => $trend_actual
                    ],
                    'completion' => [
                        'labels' => $completion_labels,
                        'data' => $completion_data
                    ],
                    'timing' => [
                        'labels' => $timing_labels,
                        'data' => $timing_data
                    ],
                    'progress' => [
                        'labels' => $progress_labels,
                        'data' => $progress_data
                    ],
                    'status' => [
                        'labels' => $status_labels,
                        'data' => $status_data
                    ],
                    'range' => [
                        'labels' => $range_labels,
                        'data' => $range_data
                    ]
                ];
            } else {
                // 默认数据
                $chart_data = [
                    'trend' => [
                        'months' => ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        'planned' => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                        'actual' => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    ],
                    'completion' => [
                        'labels' => ['请选择项目'],
                        'data' => [1]
                    ],
                    'timing' => [
                        'labels' => ['请选择项目'],
                        'data' => [1]
                    ],
                    'progress' => [
                        'labels' => ['请选择项目'],
                        'data' => [1]
                    ],
                    'status' => [
                        'labels' => ['请选择项目'],
                        'data' => [1]
                    ],
                    'range' => [
                        'labels' => ['请选择项目'],
                        'data' => [1]
                    ]
                ];
            }
            ?>

            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text">¥<?php echo number_format($contract_total, 1); ?>万</h2>
                            <p class="text-info"><?php echo $gcid > 0 ? '项目ID: ' . $gcid : '请选择项目'; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">已收款总额</h5>
                            <h2 class="card-text">¥<?php echo number_format($received_total, 1); ?>万</h2>
                            <p class="<?php echo $payment_rate >= 80 ? 'text-success' : ($payment_rate >= 60 ? 'text-warning' : 'text-danger'); ?>">
                                <?php echo $payment_rate; ?>% 收款率
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">项目管理费</h5>
                            <h2 class="card-text">¥<?php echo number_format($management_fee, 1); ?>万</h2>
                            <p class="text-success">10% 取费率</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">项目投资额</h5>
                            <h2 class="card-text">¥<?php echo number_format($investment_amount, 1); ?>万</h2>
                            <p class="text-success">
                                <?php
                                if ($investment_amount >= 5000) echo '大型项目';
                                elseif ($investment_amount >= 1000) echo '中型项目';
                                elseif ($investment_amount > 0) echo '小型项目';
                                else echo '未设定';
                                ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同到账趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款完成率分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentCompletionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同基本信息</h5>
                        </div>
                        <?php
                        $sql="SELECT * FROM `tuqoa_htgl` WHERE `projectid`=$gcid";
                        $htid=0;
                        $result = mysqli_query($link, $sql);
                        if ($result) {
                            while ($row1 = mysqli_fetch_assoc($result)) {
                                $htid=($row1["id"]);
                        ?>
                        <div class="card-body">
                            <div class="table-responsive">
                                <ul class="nav nav-tabs" id="projectDetailTabs" role="tablist" style="height: 800px;">
                                    <iframe src="/task.php?a=p&num=htgl&mid=<?php echo $htid; ?>" width="100%" style="height: 600px;"></iframe>
                                </ul>
                            </div>
                        </div>
                        <?php
                            }
                        } else {
                            echo "<div class='card-body'><p>数据库查询错误: " . mysqli_error($link) . "</p></div>";
                        }
                        ?>
                    </div>
                </div>
            </div>


            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款时效分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTimingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款进度</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentProgressChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目管理费用明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>费用类型</th>
                                            <th>计划金额</th>
                                            <th>实际金额</th>
                                            <th>差额</th>
                                            <th>占比</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>人工费用</td>
                                            <td>¥30万</td>
                                            <td>¥32万</td>
                                            <td class="text-danger">+¥2万</td>
                                            <td>40%</td>
                                            <td>人员增加导致</td>
                                        </tr>
                                        <tr>
                                            <td>合计</td>
                                            <td>¥80万</td>
                                            <td>¥80万</td>
                                            <td>¥0</td>
                                            <td>100%</td>
                                            <td>-</td>
                                        </tr>-->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目规模与投资分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目规模</th>
                                            <th>项目数量</th>
                                            <th>总投资额</th>
                                            <th>平均投资额</th>
                                            <th>合同总额</th>
                                            <th>平均取费率</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>大型项目</td>
                                            <td>3</td>
                                            <td>¥21600万</td>
                                            <td>¥7200万</td>
                                            <td>¥2400万</td>
                                            <td>11.1%</td>
                                            <td>投资额>5000万</td>
                                        </tr>-->
                                        
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同收款状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款金额区间分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentRangeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认项目
        document.addEventListener('DOMContentLoaded', function() {
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const selectedProject = document.getElementById('project-select').value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
                // 模拟数据刷新
                //alert('已更新数据，项目: ' + selectedProject);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 初始化图表
        function initCharts() {
            const chartData = <?php echo json_encode($chart_data); ?>;
            console.log('图表数据:', chartData);

            // 合同到账趋势图表
            const paymentTrendCtx = document.getElementById('paymentTrendChart');
            if (paymentTrendCtx) {
                new Chart(paymentTrendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: chartData.trend.months,
                        datasets: [
                            {
                                label: '计划收款',
                                data: chartData.trend.planned,
                                borderColor: '#1e88e5',
                                backgroundColor: 'rgba(30, 136, 229, 0.1)',
                                tension: 0.3,
                                fill: false,
                                pointBackgroundColor: '#1e88e5',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            },
                            {
                                label: '实际收款',
                                data: chartData.trend.actual,
                                borderColor: '#43a047',
                                backgroundColor: 'rgba(67, 160, 71, 0.1)',
                                tension: 0.3,
                                fill: false,
                                pointBackgroundColor: '#43a047',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '合同到账趋势',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': ¥' + context.parsed.y + '万';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '金额（万元）'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '月份'
                                }
                            }
                        }
                    }
                });
            }

            // 收款完成率分析图表
            const paymentCompletionCtx = document.getElementById('paymentCompletionChart');
            if (paymentCompletionCtx) {
                const completionColors = {
                    '未收款': '#f44336',
                    '部分收款': '#ff9800',
                    '完全收款': '#4caf50',
                    '其他': '#9e9e9e'
                };
                const colors = chartData.completion.labels.map(label => completionColors[label] || '#9e9e9e');

                new Chart(paymentCompletionCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: chartData.completion.labels,
                        datasets: [{
                            data: chartData.completion.data,
                            backgroundColor: colors,
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '收款完成率分析',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '笔 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // 收款时效分析图表
            const paymentTimingCtx = document.getElementById('paymentTimingChart');
            if (paymentTimingCtx) {
                const timingColors = {
                    '未收款': '#9e9e9e',
                    '提前收款': '#2196f3',
                    '按时收款': '#4caf50',
                    '延迟收款': '#ff9800',
                    '严重延迟': '#f44336'
                };
                const colors = chartData.timing.labels.map(label => timingColors[label] || '#9e9e9e');

                new Chart(paymentTimingCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: chartData.timing.labels,
                        datasets: [{
                            data: chartData.timing.data,
                            backgroundColor: colors,
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '50%',
                        plugins: {
                            title: {
                                display: true,
                                text: '收款时效分析',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '笔 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // 收款进度图表
            const paymentProgressCtx = document.getElementById('paymentProgressChart');
            if (paymentProgressCtx) {
                new Chart(paymentProgressCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: chartData.progress.labels,
                        datasets: [{
                            data: chartData.progress.data,
                            backgroundColor: ['#43a047', '#e0e0e0'],
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '60%',
                        plugins: {
                            title: {
                                display: true,
                                text: '收款进度',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                        return context.label + ': ¥' + context.parsed + '万 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // 合同收款状态分布图表
            const contractStatusCtx = document.getElementById('contractStatusChart');
            if (contractStatusCtx) {
                const statusColors = {
                    '未收款': '#f44336',
                    '部分收款': '#ff9800',
                    '收款完成': '#4caf50',
                    '已结算': '#2196f3'
                };
                const colors = chartData.status.labels.map(label => statusColors[label] || '#9e9e9e');

                new Chart(contractStatusCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.status.labels,
                        datasets: [{
                            label: '合同数量',
                            data: chartData.status.data,
                            backgroundColor: colors,
                            borderColor: colors.map(color => color.replace('f4', 'c6')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '合同收款状态分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '合同数量: ' + context.parsed.y + '笔';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '合同数量'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '收款状态'
                                }
                            }
                        }
                    }
                });
            }

            // 收款金额区间分布图表
            const paymentRangeCtx = document.getElementById('paymentRangeChart');
            if (paymentRangeCtx) {
                const rangeColors = ['#9e9e9e', '#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0'];
                new Chart(paymentRangeCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.range.labels,
                        datasets: [{
                            label: '收款笔数',
                            data: chartData.range.data,
                            backgroundColor: rangeColors.slice(0, chartData.range.labels.length),
                            borderColor: rangeColors.slice(0, chartData.range.labels.length).map(color => color.replace('e5', 'c0')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '收款金额区间分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '收款笔数: ' + context.parsed.y + '笔';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '收款笔数'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '金额区间'
                                }
                            }
                        }
                    }
                });
            }
        }

        // 生成颜色数组的辅助函数
        function generateColors(count) {
            const baseColors = [
                '#1e88e5', '#e53935', '#43a047', '#ffb300',
                '#8e24aa', '#00acc1', '#6d4c41', '#757575',
                '#f44336', '#9c27b0', '#3f51b5', '#2196f3',
                '#009688', '#4caf50', '#8bc34a', '#cddc39',
                '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'
            ];

            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(baseColors[i % baseColors.length]);
            }
            return colors;
        }

        // 调试函数
        function debugChartData() {
            console.log('=== 项目合同到账明细图表数据调试信息 ===');
            console.log('选中项目ID:', <?php echo $gcid; ?>);
            console.log('合同总额:', <?php echo $contract_total; ?>);
            console.log('已收款总额:', <?php echo $received_total; ?>);
            console.log('收款率:', <?php echo $payment_rate; ?>);
            console.log('图表数据:', <?php echo json_encode($chart_data); ?>);
            console.log('Chart.js 版本:', Chart.version);
        }

        // 页面加载完成后调用调试函数
        window.addEventListener('load', function() {
            debugChartData();
        });
    </script>
<?php mysqli_close($link); ?>
</body>
</html> 