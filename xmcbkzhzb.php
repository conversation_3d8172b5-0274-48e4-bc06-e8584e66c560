<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$defaultMonth = date('Y-m');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start-month'])) {
    $selectedMonth = $_POST['start-month'];
    // 可以在这里进行验证，确保格式正确
} else {
    $selectedMonth = $defaultMonth;
}
$nf = substr($selectedMonth, 0, 4);
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>项目成本控制汇总表</title>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 300px;
        }
        
        .card:hover .card-header::before {
            left: 100%;
        }
        
        .card-title {
            color: white !important;
            font-weight: 600;
            margin-bottom: 0;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }


        /* 页面特定样式 - 表格容器 */
        .table-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            margin: 20px 0;
            border: 1px solid rgba(229, 231, 235, 0.8);
        }

        .table-wrapper {
            width: 100%;
            height: calc(100vh - 180px);
            min-height: 500px;
            overflow: auto;
            position: relative;
        }

        /* 自定义滚动条 */
        .table-wrapper::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 6px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            border-radius: 6px;
            border: 2px solid #f8fafc;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
        }

        .table-wrapper::-webkit-scrollbar-corner {
            background: #f8fafc;
        }

        table {
            width: 100%;
            min-width: 1600px;
            font-size: 13px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            line-height: 1.5;
        }

        /* 现代化表头样式 */
        thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            color: white;
            font-weight: 600;
            font-size: 12px;
            text-align: center;
            padding: 16px 10px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.25);
            border-right: 1px solid rgba(255,255,255,0.2);
            letter-spacing: 0.5px;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.15);
            vertical-align: middle;
            line-height: 1.3;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        thead th:first-child {
            border-top-left-radius: 12px;
            padding-left: 20px;
            text-align: left;
        }

        thead th:last-child {
            border-top-right-radius: 12px;
            border-right: none;
        }

        /* 现代化表格行样式 */
        tbody tr {
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #e5e7eb;
            background: white;
        }

        tbody tr:hover {
            background: linear-gradient(90deg, rgba(30, 64, 175, 0.06) 0%, rgba(37, 99, 235, 0.02) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
            border-color: rgba(30, 64, 175, 0.2);
        }

        tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.5);
        }

        tbody tr:nth-child(even):hover {
            background: linear-gradient(90deg, rgba(30, 64, 175, 0.08) 0%, rgba(37, 99, 235, 0.03) 100%);
        }

        /* 统一表格数据基础样式 */
        tbody td {
            padding: 14px 12px;
            border-right: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            color: #4b5563;
            font-size: 13px;
            font-weight: 500;
            vertical-align: middle;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.4;
        }

        tbody td:first-child {
            padding-left: 20px;
            font-weight: 600;
            color: #4b5563;
            font-size: 13px;
        }

        tbody td:last-child {
            border-right: none;
        }

        tbody tr:hover td {
            color: #1f2937;
            border-color: rgba(30, 64, 175, 0.2);
        }

        /* 数据高亮样式 */
        .highlight {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            color: white !important;
            font-weight: 600;
            border-radius: 6px;
            text-align: center;
            padding: 10px 12px;
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
            font-size: 12px;
            letter-spacing: 0.3px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 日期列样式 */
        tbody td:nth-child(3),  /* 开工日期 */
        tbody td:nth-child(4) { /* 竣工日期 */
            font-family: 'SF Mono', 'Monaco', 'Roboto Mono', monospace;
            font-size: 12px;
            color: #4b5563;
            font-weight: 500;
            white-space: nowrap;
            text-align: center;
            letter-spacing: 0.5px;
        }

        /* 金额列样式 */
        tbody td:nth-child(5),  /* 合同金额 */
        tbody td:nth-child(10), /* 本月产值 */
        tbody td:nth-child(11), /* 本年累计 */
        tbody td:nth-child(12), /* 累计产值 */
        tbody td:nth-child(13), /* 预算成本 */
        tbody td:nth-child(14), /* 实际成本 */
        tbody td:nth-child(16), /* 预算直接费 */
        tbody td:nth-child(17), /* 实际直接费 */
        tbody td:nth-child(19), /* 应回收款 */
        tbody td:nth-child(20) { /* 实际收款 */
            font-family: 'SF Mono', 'Monaco', 'Roboto Mono', monospace;
            font-weight: 600;
            color: #4b5563;
            text-align: right;
            font-size: 13px;
            letter-spacing: 0.3px;
        }

        /* 百分比和进度列样式 */
        tbody td:nth-child(6),  /* 计划进度 */
        tbody td:nth-child(7),  /* 实际进度 */
        tbody td:nth-child(9),  /* 完成率 */
        tbody td:nth-child(15), /* 成本占比 */
        tbody td:nth-child(18) { /* 直接费占比 */
            text-align: center;
            font-weight: 600;
            font-size: 13px;
            color: #4b5563;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 文本描述列样式 */
        tbody td:nth-child(8) { /* 偏差原因 */
            text-align: left;
            font-weight: 500;
            font-size: 13px;
            color: #4b5563;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 建设单位和项目名称样式 */
        tbody td:nth-child(1), /* 建设单位 */
        tbody td:nth-child(2) { /* 项目名称 */
            font-weight: 600;
            color: #4b5563;
            font-size: 13px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.4;
        }

        tbody tr:hover td:nth-child(1),
        tbody tr:hover td:nth-child(2) {
            color: #1e40af;
            font-weight: 700;
        }

        /* 合同金额特殊强调样式 */
        tbody td:nth-child(5) {
            font-weight: 700;
            color: #374151;
            font-size: 14px;
        }

        /* 悬停时的字体增强 */
        tbody tr:hover td {
            font-weight: 600;
        }

        /* 金额数据悬停效果 */
        tbody tr:hover td:nth-child(5),
        tbody tr:hover td:nth-child(10),
        tbody tr:hover td:nth-child(11),
        tbody tr:hover td:nth-child(12),
        tbody tr:hover td:nth-child(13),
        tbody tr:hover td:nth-child(14),
        tbody tr:hover td:nth-child(16),
        tbody tr:hover td:nth-child(17),
        tbody tr:hover td:nth-child(19),
        tbody tr:hover td:nth-child(20) {
            color: #000000;
            font-weight: 700;
        }

        /* 百分比数据悬停效果 */
        tbody tr:hover td:nth-child(6),
        tbody tr:hover td:nth-child(7),
        tbody tr:hover td:nth-child(9),
        tbody tr:hover td:nth-child(15),
        tbody tr:hover td:nth-child(18) {
            color: #1f2937;
            font-weight: 700;
        }

        /* 日期数据悬停效果 */
        tbody tr:hover td:nth-child(3),
        tbody tr:hover td:nth-child(4) {
            color: #4b5563;
            font-weight: 600;
        }

        /* 合同金额悬停特殊效果 */
        tbody tr:hover td:nth-child(5) {
            color: #b91c1c;
            font-weight: 800;
            text-shadow: 0 1px 2px rgba(185, 28, 28, 0.1);
        }

        /* 空数据状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
            font-size: 16px;
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
        }

        .empty-state i {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 16px;
            display: block;
        }

        /* 表格标题增强 */
        .table-title {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px 24px;
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            letter-spacing: 0.5px;
            border-radius: 16px 16px 0 0;
        }

        .table-subtitle {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
            padding: 12px 24px;
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            border-bottom: 1px solid rgba(79, 70, 229, 0.1);
        }

        /* 页面特定进度条样式 */
        .progress-container {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 120px;
        }

        .progress-bar {
            width: 70px;
            height: 8px;
            background: linear-gradient(90deg, #e2e8f0 0%, #f1f5f9 100%);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #374151 0%, #000000 100%);
            border-radius: 10px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            min-width: 35px;
        }



        /* 响应式设计 */
        @media (max-width: 768px) {
            .table-wrapper {
                height: calc(100vh - 320px);
            }
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-spreadsheet me-2"></i>
                项目成本控制汇总表
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-month">
                        <i class="bx bx-calendar me-1"></i>开始月份:
                    </label>
                    <input type="month" id="start-month" name="start-month"
                           value="<?php echo isset($_POST['start-month']) ? htmlspecialchars($_POST['start-month']) : date('Y-m'); ?>">
                    <button type="submit" id="query-btn">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                    <button type="button" onclick="method5('tableExcel')">
                        <i class="bx bx-download me-1"></i>导出Excel
                    </button>
                </div>
                </form>
            </div>

        <div class="table-container">
            <div class="table-wrapper">
                <table id="tableExcel">
            <thead>
                <tr>

                    <th>建设单位</th>
                    <th>项目名称</th>
                    <th>开工日期</th>
                    <th>竣工日期</th>
                    <th>合同金额</th>
                    <th>计划进度</th>
                    <th>实际进度</th>
                    <th>偏差原因</th>
                    <th>完成率</th>
                    <th>本月产值</th>
                    <th>本年累计</th>
                    <th>累计产值</th>
                    <th>预算成本</th>
                    <th>实际成本</th>
                    <th>成本占比</th>
                    <th>预算直接费</th>
                    <th>实际直接费</th>
                    <th>直接费占比</th>
                    <th>应回收款</th>
                    <th>实际收款</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                $result = mysqli_query($link, $sql);

                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                ?>
                <tr>

                    <td><?=$row["jsname"]?></td>
                    <td><?=$row["gcname"]?></td>
                    <td class="nowrap"><?=$row["jhstartdt"]?></td>
                    <td class="nowrap"><?=$row["jhenddt"]?></td>
                    <td class="text-right"><?=$row["zaojia"]?>万</td>
                    <?php
                    $jhjd="";
                    $sjjd="";
                    $pcyy="";
                    $wcl=0;
                    $wccz=0;
                     $sql1="SELECT *  FROM `tuqoa_xmcztjb` where projectid=".$row["id"]." and `sbrq` like '$selectedMonth%' order by id desc";
                     $result1 = mysqli_query($link, $sql1);

                     if ($result1) {
                         while ($row1 = mysqli_fetch_assoc($result1)) {
                             $jhjd=$row1["jhjd"];
                             $sjjd=$row1["sjjd"];
                             $pcyy=$row1["pcyy"];
                             $wcl=$row1["wcl"];
                             $wccz=$row1["wccz"];
                         }
                     }
                    ?>
                    <td class="text-center"><?=$jhjd?></td>
                    <td class="text-center"><?=$sjjd?></td>
                    <td><?=$pcyy?></td>
                    <td class="text-center">
                        <div class="progress-container">
                            <span class="progress-text"><?=$wcl?>%</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?=$wcl?>%"></div>
                            </div>
                        </div>
                    </td>
                    <td class="text-right"><?=$wccz?>万</td>
                    <?php
                    $sql1="select IFNULL(SUM(wccz), 0) as wccznd from tuqoa_xmcztjb where projectid=".$row["id"]." and sbrq like '$nf%'";
                    $result1 = mysqli_query($link, $sql1);
                    $wccznd = 0;
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $wccznd=$row1["wccznd"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$wccznd?>万</td>
                    <?php
                    $sql1="select IFNULL(sum(wccz),0) as wcczlj from tuqoa_xmcztjb where projectid=".$row["id"]."";
                    $result1 = mysqli_query($link, $sql1);
                    $wcczlj = 0;
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $wcczlj=$row1["wcczlj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$wcczlj?>万</td>
                    <?php
                    //$实际直接费=0;
                    $actual_direct_cost=0;
                    $total_salary=0;
                    $actual_cost=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            //print_r($row1["dpryxm"]);
                            $sql2="SELECT * FROM `tuqoa_hrsalary` WHERE `uname`='".$row1["dpryxm"]."' and `month`='".$selectedMonth."'";
                            //print_r($sql2."<br>");
                            $result2 = mysqli_query($link, $sql2);
                            if ($result2) {
                                while ($row2 = mysqli_fetch_assoc($result2)) {
                                    $total_salary+=$row2["yfgz"];
                                }
                            }
                        }
                    }
                    //福利费用等获取
                    $social_insurance_total=0;
                    $sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $social_insurance_total=$row1["hj"];
                        }
                    }
                    ?>
                    <?php
                    $budget_cost=0;
                    $budget_direct_cost=0;
                    $management_fee=0;
                    $business_fee=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $budget_cost=$row1["yszcbfy"];
                            $budget_direct_cost=$row1["yszjf"];
                            $management_fee=$row1["qyglf"];
                            $business_fee=$row1["jyywf"];
                            //$实际总成本费用=$row1["gmxzyp"]+$row1["bgf"]+$row1["zjf"]+$row1["dzyhptx"]+$row1["clf"]+$row1["qtfy"]+$row1["zbfwf"]+$row1["qyglf"]+$row1["jyywf"]+$row1["lr"]+$row1["sj"];
                        }
                    }
                    $actual_cost=$total_salary+$social_insurance_total+$management_fee+$business_fee;
                    $actual_direct_cost=$total_salary+$social_insurance_total;
                    ?>
                    <td class="text-right"><?=$budget_cost?></td>
                    <?php
                    // 空的PHP代码块，可以删除或添加其他逻辑
                    ?>
                    <td class="text-right"><?=$actual_cost?></td>
                    <?php
                    $cost_ratio=0;
                    $direct_cost_ratio=0;
                    if($budget_cost>0){
                         $cost_ratio=number_format(($actual_cost/$budget_cost)*100,2);
                         $direct_cost_ratio=number_format(($actual_direct_cost/$budget_direct_cost)*100,2);
                    }
                    ?>
                    <td class="text-center"><?=$cost_ratio?>%</td>
                    <td class="text-right"><?=$budget_direct_cost?></td>
                    <td class="text-right"><?=$actual_direct_cost?>万</td>
                    <?php
                    // 空的PHP代码块
                    ?>
                    <td class="text-center"><?=$direct_cost_ratio?>%</td>
                    <?php
                    $expected_payment=0;
                    $sql1="SELECT ifnull(sum(yjje),0) as  yjjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `yjsj` like '$selectedMonth%'";
                    //print_r($sql1."<br>");
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $expected_payment=$row1["yjjehj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$expected_payment?>万</td>
                    <?php
                    $actual_payment=0;
                    $sql1="SELECT ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `sksj` like '$selectedMonth%'";
                    //print_r($sql1."<br>");
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $actual_payment=$row1["ysjehj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$actual_payment?>万</td>
                    <?php

                    } // 结束while循环
                } else {
                    echo "<tr><td colspan='15'>查询错误: " . mysqli_error($link) . "</td></tr>";
                }
                ?>
                </tbody>
                </table>
            </div>
        </div>

        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示测试提示
            document.getElementById('test-alert').style.display = 'block';
            setTimeout(function() {
                document.getElementById('test-alert').style.display = 'none';
            }, 3000);

            // 简洁的表格交互效果
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach((row, index) => {
                // 添加延迟动画效果
                row.style.opacity = '0';
                row.style.transform = 'translateY(10px)';

                setTimeout(() => {
                    row.style.transition = 'all 0.3s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 30);

                // 简化悬停效果
                row.addEventListener('mouseenter', function() {
                    this.style.zIndex = '2';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.zIndex = '1';
                });
            });

            // 简化进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 0.8s ease';
                    bar.style.width = width;
                }, 500 + index * 50);
            });

            // 添加表格头部固定效果增强
            const tableWrapper = document.querySelector('.table-wrapper');
            if (tableWrapper) {
                tableWrapper.addEventListener('scroll', function() {
                    const scrollTop = this.scrollTop;
                    const headers = document.querySelectorAll('thead th');
                    headers.forEach(header => {
                        if (scrollTop > 0) {
                            header.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                        } else {
                            header.style.boxShadow = 'none';
                        }
                    });
                });
            }
        });
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 页面加载完成后初始化时间更新
        document.addEventListener('DOMContentLoaded', function() {
            // 更新最后更新时间
            updateLastUpdateTime();
            
            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>