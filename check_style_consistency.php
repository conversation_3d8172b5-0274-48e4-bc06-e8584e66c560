<?php
/**
 * 检查所有PHP文件的风格一致性
 * 确保与ssjdgzhz.php的风格完全一致
 */

// 获取所有PHP文件
$phpFiles = glob('*.php');
$issues = [];
$checkedFiles = [];

// 标准风格要求
$requiredElements = [
    'bootstrap_css' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
    'boxicons_css' => 'https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css',
    'fontawesome_css' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'main_css' => 'styles/main.css',
    'chartjs' => 'https://cdn.jsdelivr.net/npm/chart.js',
    'navbar_gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'card_header_gradient' => 'linear-gradient(135deg, #007bff, #0056b3)'
];

foreach ($phpFiles as $file) {
    if ($file === 'check_style_consistency.php') continue;
    
    $content = file_get_contents($file);
    $fileIssues = [];
    
    // 检查DOCTYPE
    if (!preg_match('/<!DOCTYPE html>/i', $content)) {
        $fileIssues[] = '缺少 DOCTYPE html 声明';
    }
    
    // 检查Bootstrap CSS
    if (!strpos($content, $requiredElements['bootstrap_css'])) {
        $fileIssues[] = '缺少或错误的 Bootstrap CSS 链接';
    }
    
    // 检查Boxicons CSS
    if (!strpos($content, $requiredElements['boxicons_css'])) {
        $fileIssues[] = '缺少或错误的 Boxicons CSS 链接';
    }
    
    // 检查Font Awesome CSS
    if (!strpos($content, $requiredElements['fontawesome_css'])) {
        $fileIssues[] = '缺少或错误的 Font Awesome CSS 链接';
    }
    
    // 检查统一CSS文件
    if (!strpos($content, $requiredElements['main_css'])) {
        $fileIssues[] = '缺少统一的 main.css 文件引用';
    }
    
    // 检查Chart.js
    if (strpos($content, 'chart') !== false && !strpos($content, $requiredElements['chartjs'])) {
        $fileIssues[] = '缺少或错误的 Chart.js 链接';
    }
    
    // 检查导航栏
    if (!preg_match('/<nav[^>]*class[^>]*navbar[^>]*>/i', $content)) {
        $fileIssues[] = '缺少统一的导航栏';
    }
    
    // 检查页面标题格式
    if (!preg_match('/<title>[^<]+ - 公司数据总览系统<\/title>/i', $content)) {
        $fileIssues[] = '页面标题格式不统一';
    }
    
    // 检查是否有过多的内联样式
    $inlineStyleCount = preg_match_all('/<style[^>]*>.*?<\/style>/is', $content, $matches);
    if ($inlineStyleCount > 1) {
        $fileIssues[] = '内联样式过多，应使用统一的CSS文件';
    }
    
    // 检查是否使用了旧的CSS文件引用
    if (strpos($content, 'styles/bootstrap.min.css') || 
        strpos($content, 'styles/boxicons.min.css') || 
        strpos($content, 'styles//boxicons.min.css')) {
        $fileIssues[] = '使用了本地CSS文件，应使用CDN链接';
    }
    
    if (!empty($fileIssues)) {
        $issues[$file] = $fileIssues;
    } else {
        $checkedFiles[] = $file;
    }
}

// 输出检查结果
echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>风格一致性检查报告 - 公司数据总览系统</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "    <link href='styles/main.css' rel='stylesheet'>\n";
echo "</head>\n";
echo "<body>\n";

echo "<nav class='navbar navbar-expand-lg'>\n";
echo "    <div class='container-fluid'>\n";
echo "        <a class='navbar-brand' href='#'>\n";
echo "            <i class='bx bx-check-circle me-2'></i>\n";
echo "            风格一致性检查报告\n";
echo "        </a>\n";
echo "        <div class='navbar-nav ms-auto'>\n";
echo "            <span class='navbar-text text-white'>\n";
echo "                <i class='bx bx-time me-1'></i>\n";
echo "                检查时间: " . date('Y-m-d H:i:s') . "\n";
echo "            </span>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "</nav>\n";

echo "<div class='container-fluid mt-4'>\n";

// 统计信息
$totalFiles = count($phpFiles) - 1; // 排除检查脚本本身
$passedFiles = count($checkedFiles);
$failedFiles = count($issues);

echo "<div class='row mb-4'>\n";
echo "    <div class='col-md-4'>\n";
echo "        <div class='card stat-card stat-card-primary'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-file-code stat-icon'></i>\n";
echo "                <h5 class='card-title'>总文件数</h5>\n";
echo "                <h2 class='card-text'>$totalFiles</h2>\n";
echo "                <p class='stat-info'>PHP文件总数</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "    <div class='col-md-4'>\n";
echo "        <div class='card stat-card stat-card-success'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-check-circle stat-icon'></i>\n";
echo "                <h5 class='card-title'>通过检查</h5>\n";
echo "                <h2 class='card-text'>$passedFiles</h2>\n";
echo "                <p class='stat-info'>风格一致的文件</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "    <div class='col-md-4'>\n";
echo "        <div class='card stat-card stat-card-" . ($failedFiles > 0 ? 'warning' : 'success') . "'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-exclamation-triangle stat-icon'></i>\n";
echo "                <h5 class='card-title'>需要修改</h5>\n";
echo "                <h2 class='card-text'>$failedFiles</h2>\n";
echo "                <p class='stat-info'>存在问题的文件</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "</div>\n";

// 显示通过检查的文件
if (!empty($checkedFiles)) {
    echo "<div class='card mb-4'>\n";
    echo "    <div class='card-header'>\n";
    echo "        <h5 class='card-title mb-0'><i class='fas fa-check-circle me-2'></i>通过检查的文件 ($passedFiles)</h5>\n";
    echo "    </div>\n";
    echo "    <div class='card-body'>\n";
    echo "        <div class='row'>\n";
    foreach ($checkedFiles as $file) {
        echo "            <div class='col-md-3 mb-2'>\n";
        echo "                <span class='badge bg-success'><i class='fas fa-check me-1'></i>$file</span>\n";
        echo "            </div>\n";
    }
    echo "        </div>\n";
    echo "    </div>\n";
    echo "</div>\n";
}

// 显示存在问题的文件
if (!empty($issues)) {
    echo "<div class='card mb-4'>\n";
    echo "    <div class='card-header'>\n";
    echo "        <h5 class='card-title mb-0'><i class='fas fa-exclamation-triangle me-2'></i>需要修改的文件 ($failedFiles)</h5>\n";
    echo "    </div>\n";
    echo "    <div class='card-body'>\n";
    foreach ($issues as $file => $fileIssues) {
        echo "        <div class='alert alert-warning'>\n";
        echo "            <h6><i class='fas fa-file-code me-2'></i><strong>$file</strong></h6>\n";
        echo "            <ul class='mb-0'>\n";
        foreach ($fileIssues as $issue) {
            echo "                <li>$issue</li>\n";
        }
        echo "            </ul>\n";
        echo "        </div>\n";
    }
    echo "    </div>\n";
    echo "</div>\n";
}

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
