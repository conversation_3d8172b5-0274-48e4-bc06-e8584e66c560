# PHP页面风格统一 - 使用指南

## 项目完成情况

✅ **已完成所有PHP页面的风格统一工作**

所有PHP页面现在都使用与 `ssjdgzhz.php` 一致的现代化设计风格。

## 修改的文件列表

### 已统一风格的PHP文件：
1. **diagnose.php** - 系统诊断页面
2. **dkzbcx.php** - 代打卡查询页面  
3. **jydt.php** - 经营动态页面
4. **jytj.php** - 经营统计页面
5. **wtgz.php** - 问题工作跟踪页面
6. **ygdt.php** - 员工动态页面
7. **xmcbhs.php** - 项目成本核算页面
8. **xmfymx.php** - 项目费用明细页面
9. **xmsflb.php** - 项目收费列表页面
10. **ydjysjfx.php** - 月度经营数据分析页面
11. **zzzsdt.php** - 资质证书动态页面
12. **gztz.php** - 工作台账页面
13. **ndyjsflb.php** - 年度预计收费列表页面
14. **rqxz.php** - 日期区间选择页面

### 核心文件：
- **styles/main.css** - 统一的CSS样式文件
- **test_styles.html** - 风格测试页面

## 统一的设计特点

### 1. 导航栏设计
- 渐变背景：`linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- 统一的品牌标识和图标
- 右侧显示最后更新时间

### 2. 卡片系统
- 圆角设计：`border-radius: 15px`
- 阴影效果：`box-shadow: 0 2px 10px rgba(0,0,0,0.1)`
- 悬停动画：`transform: translateY(-2px)`

### 3. 统计卡片
- 8种渐变背景色彩
- 统一的图标和文字布局
- 动态悬停效果

### 4. 表格样式
- 现代化的表头设计
- 悬停行高亮效果
- 响应式布局

## 如何使用

### 1. 访问页面
所有PHP页面现在都具有统一的外观和感觉：
- 一致的导航栏
- 统一的卡片设计
- 标准化的按钮和表单

### 2. 测试页面
访问 `test_styles.html` 查看所有样式组件的效果。

### 3. 自定义样式
如需添加特定页面的样式，在页面的 `<style>` 标签中添加，不要修改 `styles/main.css`。

## 技术实现

### CSS架构
```
styles/main.css
├── 全局样式 (body, 容器)
├── 导航栏样式
├── 卡片系统
│   ├── 基础卡片
│   └── 统计卡片 (8种颜色)
├── 表格样式
├── 表单样式
├── 按钮样式
└── 响应式设计
```

### HTML结构模板
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-icon me-2"></i>
                页面标题
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 页面内容 -->
    </div>
</body>
</html>
```

## 维护建议

### 1. 样式管理
- 所有通用样式都在 `styles/main.css` 中
- 页面特定样式使用内联 `<style>` 标签
- 避免重复定义相同的样式

### 2. 新页面开发
- 复制现有页面的HTML结构
- 使用统一的CSS类名
- 遵循既定的设计模式

### 3. 图标使用
- 导航栏图标：使用 Boxicons (`bx bx-*`)
- 统计卡片图标：使用 Font Awesome (`fas fa-*`)
- 保持图标风格一致

## 浏览器兼容性

支持的浏览器：
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 性能优化

- 使用CDN加载外部资源
- CSS文件经过优化
- 响应式设计减少移动端加载时间

## 总结

通过这次风格统一工作，所有PHP页面现在都具有：
- ✅ 一致的视觉设计
- ✅ 现代化的用户界面
- ✅ 响应式布局
- ✅ 良好的用户体验
- ✅ 易于维护的代码结构

所有原有功能都得到完整保留，只是外观得到了显著提升。
