<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 确保日期格式正确
if (isset($_POST['start-date']) && $_POST['start-date']) {
    $startDate = $_POST['start-date'] . '-01'; // 添加日期部分
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    $endDate = date('Y-m-t', strtotime($_POST['end-date'] . '-01')); // 获取月份的最后一天
}
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月', strtotime($startDate));
        $displayEnd = date('Y年m月', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目费用明细 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .table-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            margin: 20px 0;
            border: 1px solid rgba(229, 231, 235, 0.8);
        }

        .table-wrapper {
            width: 100%;
            height: calc(100vh - 180px);
            min-height: 500px;
            overflow: auto;
            position: relative;
        }

        /* 自定义滚动条 */
        .table-wrapper::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 6px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            border-radius: 6px;
            border: 2px solid #f8fafc;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
        }

        .table-wrapper::-webkit-scrollbar-corner {
            background: #f8fafc;
        }

        table {
            width: 100%;
            min-width: 1800px;
            font-size: 13px;
            border-collapse: separate;
            border-spacing: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* 现代化多级表头样式 */
        thead {
            position: sticky;
            top: 0;
            z-index: 15;
        }

        thead th {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            color: white;
            font-weight: 600;
            font-size: 13px;
            text-align: center;
            padding: 14px 12px;
            border: 1px solid rgba(255,255,255,0.2);
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            position: relative;
            letter-spacing: 0.3px;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.15);
        }

        thead th:first-child {
            border-top-left-radius: 8px;
        }

        thead th:last-child {
            border-top-right-radius: 8px;
        }

        .sub-header th {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            top: 47px;
            font-size: 12px;
            padding: 12px 10px;
            font-weight: 600;
            letter-spacing: 0.2px;
            position: sticky;
        }

        .sub-sub-header th {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            top: 82px;
            font-size: 11px;
            padding: 10px 8px;
            font-weight: 600;
            letter-spacing: 0.1px;
            position: sticky;
        }

        /* 现代化表格行样式 */
        tbody tr {
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #e5e7eb;
            background: white;
        }

        tbody tr:hover {
            background: linear-gradient(90deg, rgba(30, 64, 175, 0.06) 0%, rgba(37, 99, 235, 0.02) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
            border-color: rgba(30, 64, 175, 0.2);
        }

        tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.5);
        }

        tbody tr:nth-child(even):hover {
            background: linear-gradient(90deg, rgba(30, 64, 175, 0.08) 0%, rgba(37, 99, 235, 0.03) 100%);
        }

        tbody td {
            padding: 12px 10px;
            border-right: 1px solid #e5e7eb;
            white-space: nowrap;
            color: #374151;
            text-align: right;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
            vertical-align: middle;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        tbody td:first-child {
            text-align: left;
            font-weight: 600;
            color: #1f2937;
            font-size: 13px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 16px;
        }

        tbody tr:hover td {
            color: #1f2937;
            border-color: rgba(30, 64, 175, 0.2);
        }

        tbody tr:hover td:first-child {
            color: #1e40af;
            font-weight: 600;
        }

        /* 数据高亮样式 */
        .highlight {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white !important;
            font-weight: 700;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
            font-size: 13px;
            letter-spacing: 0.5px;
        }

        /* 金额数据样式 */
        tbody td[style*="text-align: right"],
        .text-right {
            font-family: 'SF Mono', 'Monaco', 'Roboto Mono', monospace;
            font-weight: 500;
            color: #4b5563;
            font-size: 13px;
            text-align: right;
        }

        /* 负数金额样式 */
        tbody td[style*="color: red"],
        .negative-amount {
            color: #dc2626 !important;
            font-weight: 600;
        }

        /* 零值样式 */
        .zero-amount {
            color: #9ca3af;
            font-style: italic;
        }

        /* 表格边框优化 */
        tbody td:last-child {
            border-right: none;
        }

        tbody tr:last-child td {
            border-bottom: none;
        }

        /* 空数据状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
            font-size: 16px;
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
        }

        .empty-state i {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 16px;
            display: block;
        }

        /* 加载状态样式 */
        .loading-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 表格标题增强 */
        .table-title {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px 24px;
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            letter-spacing: 0.5px;
            border-radius: 16px 16px 0 0;
        }

        .table-subtitle {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
            padding: 12px 24px;
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            border-bottom: 1px solid rgba(79, 70, 229, 0.1);
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-receipt me-2"></i>
                项目费用明细表
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">
                        <i class="bx bx-calendar me-1"></i>开始月份:
                    </label>
                    <input type="month" id="start-date" name="start-date"
                           value="<?php echo isset($_POST['start-date']) ? htmlspecialchars($_POST['start-date']) : date('Y-m'); ?>">
                    <label for="end-date">
                        <i class="bx bx-calendar me-1"></i>结束月份:
                    </label>
                    <input type="month" id="end-date" name="end-date"
                           value="<?php echo isset($_POST['end-date']) ? htmlspecialchars($_POST['end-date']) : date('Y-m'); ?>">
                    <button type="submit" id="query-btn">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                    <button type="button" onclick="method5('tableExcel')">
                        <i class="bx bx-download me-1"></i>导出Excel
                    </button>
                </div>
            </form>
        </div>
        <div class="table-container">
            <div class="table-wrapper">
                <table id="tableExcel">
            <thead>
                <tr>
                    <th rowspan="3">项目名称</th>
                    <th colspan="15">直接费</th>
                    <th colspan="2" rowspan="2">间接费</th>
                    <th rowspan="3">利润</th>
                    <th rowspan="3">税金</th>
                </tr>
                <tr class="sub-header">
                    <th colspan="7">人工费</th>
                    <th colspan="8">其他直接费</th>
                </tr>
                <tr class="sub-sub-header">
                    <th>实发工资</th>
                    <th>公积金（单位+个人）</th>
                    <th>社医保（单位+个人）</th>
                    <th>伙食费</th>
                    <th>采暖费</th>
                    <th>福利费</th>
                    <th>工会经费（工资*2%）</th>
                    
                    <th>购买行政用品</th>
                    <th>办公费</th>
                    <th>折旧费</th>
                    <th>低值易耗品摊销</th>
                    <th>差旅费</th>
                    <th>其他费用</th>
                    <th>中标服务费</th>
                    <th>业务招待费</th>
                    <th>企业管理费</th>
                    
                    <th>经营业务费</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                $result = mysqli_query($link, $sql);
                if (!$result) {
                    echo "<tr><td colspan='19' style='color: red; text-align: center;'>SQL查询错误: " . mysqli_error($link) . "</td></tr>";
                    echo "<tr><td colspan='19' style='color: blue; text-align: center;'>SQL语句: " . htmlspecialchars($sql) . "</td></tr>";
                } else if (mysqli_num_rows($result) == 0) {
                    echo "<tr><td colspan='19' style='text-align: center;'>没有找到符合条件的项目数据</td></tr>";
                } else {
                    while ($row = mysqli_fetch_assoc($result)) {
                ?>
                <tr>

                    <td class="text-left"><?php echo $row["gcname"]?></td>
                    <?php
                    $actual_salary_total=0;
                    $expected_salary_total=0;
                    $housing_fund_total=0;
                    $meal_allowance_total=0;
                    $heating_fee_total=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            //print_r($row1["dpryxm"]);
                            $sql2="SELECT *  FROM `tuqoa_hrsalary` WHERE `uname`='".mysqli_real_escape_string($link, $row1["dpryxm"])."' and  `month`>='$startDate' and `month`<='$endDate'";
                            $result2 = mysqli_query($link, $sql2);
                            if ($result2) {
                                while ($row2 = mysqli_fetch_assoc($result2)) {
                                    $actual_salary_total += isset($row2["sfgz"]) ? $row2["sfgz"] : 0;
                                    $expected_salary_total += isset($row2["yfgz"]) ? $row2["yfgz"] : 0;
                                    $housing_fund_total += isset($row2["zfgjj"]) ? $row2["zfgjj"] * 2 : 0;
                                    $meal_allowance_total += isset($row2["foodbt"]) ? $row2["foodbt"] : 0;
                                    $heating_fee_total += isset($row2["cnf"]) ? $row2["cnf"] : 0;
                                }
                            }
                        }
                    }
                    $date = new DateTime($endDate);
                    $lastDayOfMonth = $date->format('Y-m-t');
                    /////////////////////////////////////////////////项目上缴社保费用明细///////////////////////////////////////////
                    $pension_total=0;
                    $medical_reimbursement_total=0;
                    $sql1="SELECT ifnull(sum(ylxj),0) ylhj,ifnull(sum(yiliaobxxj),0) yiliaobxxjhj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and  `ys`>='$startDate' and `ys`<='$endDate'";
                    //$sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $pension_total = isset($row1["ylhj"]) ? $row1["ylhj"] : 0;
                            $medical_reimbursement_total = isset($row1["yiliaobxxjhj"]) ? $row1["yiliaobxxjhj"] : 0;
                        }
                    }
                    /////////////////////////////////////////////////企业上缴社保管理费等//////////////////////////////////////////////////////////
                    //$sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
                    $welfare_fee_total=0;
                    $office_supplies_total=0;
                    $office_fee_total=0;
                    $depreciation_fee_total=0;
                    $consumables_amortization_total=0;
                    $travel_fee_total=0;
                    $other_fee_total=0;
                    $bidding_service_fee_total=0;
                    $business_entertainment_fee_total=0;
                    $enterprise_management_fee_total=0;
                    $business_operation_fee_total=0;
                    $profit_total=0;
                    $tax_total=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and  `sbrq`>='$startDate' and `sbrq`<='$endDate'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $welfare_fee_total += isset($row1["flf"]) ? $row1["flf"] : 0;
                            $office_supplies_total += isset($row1["gmxzyp"]) ? $row1["gmxzyp"] : 0;
                            $office_fee_total += isset($row1["bgf"]) ? $row1["bgf"] : 0;
                            $depreciation_fee_total += isset($row1["zzjf"]) ? $row1["zzjf"] : 0;
                            $consumables_amortization_total += isset($row1["dzyhptx"]) ? $row1["dzyhptx"] : 0;
                            $travel_fee_total += isset($row1["clf"]) ? $row1["clf"] : 0;
                            $other_fee_total += isset($row1["qtfy"]) ? $row1["qtfy"] : 0;
                            $bidding_service_fee_total += isset($row1["zbfwf"]) ? $row1["zbfwf"] : 0;
                            $business_entertainment_fee_total += isset($row1["ywzdf"]) ? $row1["ywzdf"] : 0;
                            $enterprise_management_fee_total += isset($row1["qyglf"]) ? $row1["qyglf"] : 0;
                            $business_operation_fee_total += isset($row1["jyywf"]) ? $row1["jyywf"] : 0;
                            $profit_total += isset($row1["lr"]) ? $row1["lr"] : 0;
                            $tax_total += isset($row1["sj"]) ? $row1["sj"] : 0;
                        }
                    }
                    ?>
                    <td><?php echo $actual_salary_total?></td>
                    <td><?php echo $housing_fund_total?></td>
                    <td><?php echo $pension_total+$medical_reimbursement_total?></td>
                    <td><?php echo $meal_allowance_total?></td>
                    <td><?php echo $heating_fee_total?></td>
                    <td><?php echo $welfare_fee_total?></td>
                    <td><?php echo round($expected_salary_total*0.02,2)?></td>

                    <td><?php echo $office_supplies_total?></td>
                    <td><?php echo $office_fee_total?></td>
                    <td><?php echo $depreciation_fee_total?></td>
                    <td><?php echo $consumables_amortization_total?></td>
                    <td><?php echo $travel_fee_total?></td>
                    <td><?php echo $other_fee_total?></td>
                    <td><?php echo $bidding_service_fee_total?></td>
                    <td><?php echo $business_entertainment_fee_total?></td>
                    <td><?php echo $enterprise_management_fee_total?></td>
                    <td><?php echo $business_operation_fee_total?></td>


                    <td><?php echo $profit_total?></td>
                    <td><?php echo $tax_total?></td>
                </tr>
                <?php
                    } // End while loop
                }
                ?>
            </tbody>
                </table>
            </div>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 简洁的表格加载动画
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(10px)';

                setTimeout(() => {
                    row.style.transition = 'all 0.3s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 30);
            });

            // 表格头部滚动效果
            const tableWrapper = document.querySelector('.table-wrapper');
            if (tableWrapper) {
                tableWrapper.addEventListener('scroll', function() {
                    const scrollTop = this.scrollTop;
                    const headers = document.querySelectorAll('thead th');
                    headers.forEach(header => {
                        if (scrollTop > 0) {
                            header.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                            header.style.borderBottom = '2px solid rgba(255,255,255,0.3)';
                        } else {
                            header.style.boxShadow = 'none';
                            header.style.borderBottom = '1px solid rgba(255,255,255,0.2)';
                        }
                    });
                });
            }



            // 表格行悬停增强效果
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.zIndex = '5';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.zIndex = '1';
                });
            });
        });

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 每3秒更新一次时间
        setInterval(updateTime, 3000);
    </script>


</body>
</html>