<?php
include '../config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资质证书动态 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 300px;
        }
    </style>
</head>
<?php
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-certification me-2"></i>
                资质证书动态
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">
                        <i class="bx bx-calendar me-1"></i>开始日期:
                    </label>
                    <input type="date" id="start-date" name="start-date" 
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">
                        <i class="bx bx-calendar me-1"></i>结束日期:
                    </label>
                    <input type="date" id="end-date" name="end-date" 
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit" id="query-btn">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>
            
            <div class="row">
                <?php
                $sql="SELECT count(*) zszs FROM `tuqoa_userzheng`";
                $result = mysqli_query($link, $sql);
                $zszs = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $zszs=$row["zszs"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <i class="fas fa-certificate stat-icon"></i>
                            <h5 class="card-title">总证书数</h5>
                            <h2 class="card-text"><?php echo $zszs; ?></h2>
                            <p class="text-light">累计证书总量</p>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT count(*) byxz FROM `tuqoa_userzheng` WHERE `sdt`>='$startDate' and `sdt`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $byxz = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $byxz=$row["byxz"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <i class="fas fa-plus-circle stat-icon"></i>
                            <h5 class="card-title">本月新增证书</h5>
                            <h2 class="card-text"><?php echo $byxz; ?></h2>
                            <p class="text-light">新增证书数量</p>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT count(*) bydq FROM `tuqoa_userzheng` WHERE `edt`>='$startDate' and `edt`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $bydq = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $bydq=$row["bydq"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <i class="fas fa-clock stat-icon"></i>
                            <h5 class="card-title">本月到期证书</h5>
                            <h2 class="card-text"><?php echo $bydq; ?></h2>
                            <p class="text-light">需要续期证书</p>
                        </div>
                    </div>
                </div>
                <?php
                // 由于表中没有sfzf字段，我们暂时设置为0
                $zfzs = 0;
                // $sql="SELECT count(*) zfzs FROM `tuqoa_userzheng` where mingc IS NULL";
                // $result = mysqli_query($link, $sql);
                // if ($result) {
                //     while ($row = mysqli_fetch_assoc($result)) {
                //         $zfzs=$row["zfzs"];
                //     }
                // }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-danger">
                        <div class="card-body">
                            <i class="fas fa-ban stat-icon"></i>
                            <h5 class="card-title">作废证书</h5>
                            <h2 class="card-text"><?php echo $zfzs; ?></h2>
                            <p class="text-light">已作废证书数</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?php
                // 简化的查询，获取最近6个月的证书变动数据
                $months = [];
                $acquired_count = [];
                $expired_count = [];

                // 生成最近6个月的数据
                for ($i = 5; $i >= 0; $i--) {
                    $month = date('Y-m', strtotime("-$i months"));
                    $monthLabel = date('m', strtotime("-$i months"));
                    $months[] = $monthLabel;

                    // 查询该月新增证书数量
                    $sql_acquired = "SELECT COUNT(*) as count FROM tuqoa_userzheng WHERE DATE_FORMAT(sdt, '%Y-%m') = '$month'";
                    $result_acquired = mysqli_query($link, $sql_acquired);
                    $acquired_row = mysqli_fetch_assoc($result_acquired);
                    $acquired_count[] = (int)$acquired_row['count'];

                    // 查询该月到期证书数量
                    $sql_expired = "SELECT COUNT(*) as count FROM tuqoa_userzheng WHERE DATE_FORMAT(edt, '%Y-%m') = '$month'";
                    $result_expired = mysqli_query($link, $sql_expired);
                    $expired_row = mysqli_fetch_assoc($result_expired);
                    $expired_count[] = (int)$expired_row['count'];
                }
                ?>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书变动趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="certificateTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT mingc AS lx, COUNT(*) AS sl FROM tuqoa_userzheng WHERE mingc IS NOT NULL GROUP BY mingc ORDER BY sl DESC";
                $result = mysqli_query($link, $sql);
                if (!$result) {
                    echo "查询错误: " . mysqli_error($link);
                    exit;
                }
                $deptData = ['labels' => [], 'data' => []];
                while ($row = mysqli_fetch_assoc($result)) {
                    $deptData['labels'][] = $row["lx"];
                    $deptData['data'][] = $row["sl"];
                }
                ?>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="certificateTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书到期明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>证书名称</th>
                                            <th>证书类型</th>
                                            <th>持有人</th>
                                            <th>证书编号</th>
                                            <th>到期日期</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    $sql="SELECT * FROM `tuqoa_userzheng` WHERE `edt`>='$startDate' and `edt`<='$endDate'";
                                    $result = mysqli_query($link, $sql);
                                    if (!$result) {
                                        echo "<tr><td colspan='6'>查询错误: " . mysqli_error($link) . "</td></tr>";
                                    } else {
                                        while ($row = mysqli_fetch_assoc($result)) {
                                    ?>
                                        <tr>
                                            <td><a href="/task.php?a=p&num=userzheng&mid=<?php echo $row["id"];?>" target="_blank"><?php echo $row["mingc"];?></a></td>
                                            <td><?php echo $row["mingc"];?></td>
                                            <td><?php echo $row["uname"];?></td>
                                            <td><?php echo $row["numc"];?></td>
                                            <td><?php echo $row["edt"];?></td>
                                            <td><span class="badge bg-warning">到期提醒</span></td>
                                        </tr>
                                    <?php
                                        } // end while
                                    } // end else
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    mysqli_close($link);
    ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js 未正确加载');
            alert('图表库加载失败，请检查网络连接');
        }

        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
            
            // 更新最后更新时间
            updateLastUpdateTime();
            
            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 初始化图表
        function initCharts() {
            // 检查Chart.js是否可用
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未加载');
                return;
            }
            // 证书变动趋势图表
            const trendCtx = document.getElementById('certificateTrendChart').getContext('2d');
            const labels = <?php echo json_encode($months); ?>;
            const acquiredData = <?php echo json_encode($acquired_count); ?>;
            const expiredData = <?php echo json_encode($expired_count); ?>;

            // 调试信息
            console.log('趋势图数据:', {
                labels: labels,
                acquired: acquiredData,
                expired: expiredData
            });

            // 检查趋势图数据
            if (labels.length > 0) {
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '新增证书',
                            data: acquiredData,
                            borderColor: '#000000',
                            backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            fill: true,
                            tension: 0.4
                        }, {
                            label: '到期证书',
                            data: expiredData,
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '证书数量'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '月份'
                                }
                            }
                        }
                    }
                });
            } else {
                console.error('没有可用的趋势数据');
                trendCtx.canvas.parentNode.innerHTML = '<p class="text-center text-muted">暂无趋势数据</p>';
            }

            // 证书类型分布图表
            const typeCtx = document.getElementById('certificateTypeChart').getContext('2d');
            const deptData = <?php echo json_encode($deptData); ?>;

            // 调试信息
            console.log('证书类型数据:', deptData);

            // 检查数据是否存在
            if (deptData && deptData.labels && deptData.labels.length > 0 && deptData.data && deptData.data.length > 0) {
                new Chart(typeCtx, {
                    type: 'pie',
                    data: {
                        labels: deptData.labels,
                        datasets: [{
                            data: deptData.data,
                            backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                                '#FF9F40', '#000000', '#607D8B', '#E91E63', '#FFA726'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'right',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                console.error('没有可用的证书数据');
                typeCtx.canvas.parentNode.innerHTML = '<p class="text-center text-muted">暂无证书数据</p>';
            }
        }

    </script>
</body>
</html>