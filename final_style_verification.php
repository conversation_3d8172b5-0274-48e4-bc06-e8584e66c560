<?php
/**
 * 最终风格验证脚本
 * 检查所有PHP文件是否与test_styles.html风格完全一致
 */

// 获取所有PHP文件
$phpFiles = glob('*.php');
$issues = [];
$passedFiles = [];

// 标准要求（基于test_styles.html）
$requiredElements = [
    'doctype' => '<!DOCTYPE html>',
    'bootstrap_css' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
    'boxicons_css' => 'https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css',
    'fontawesome_css' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'main_css' => 'styles/main.css',
    'chartjs' => 'https://cdn.jsdelivr.net/npm/chart.js',
    'navbar_class' => 'navbar navbar-expand-lg',
    'container_class' => 'container-fluid mt-4'
];

// 不应该存在的元素（重复样式）
$forbiddenElements = [
    'local_bootstrap' => 'styles/bootstrap.min.css',
    'local_boxicons' => 'styles/boxicons.min.css',
    'double_slash_boxicons' => 'styles//boxicons.min.css',
    'inline_navbar_style' => 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'inline_card_style' => 'border-radius: 15px',
    'inline_stat_card' => 'stat-card.*background.*linear-gradient'
];

foreach ($phpFiles as $file) {
    if (in_array($file, ['final_style_verification.php', 'check_style_consistency.php'])) continue;
    
    $content = file_get_contents($file);
    $fileIssues = [];
    
    // 检查必需元素
    if (!strpos($content, $requiredElements['doctype'])) {
        $fileIssues[] = '❌ 缺少标准DOCTYPE声明';
    }
    
    if (!strpos($content, $requiredElements['bootstrap_css'])) {
        $fileIssues[] = '❌ 缺少标准Bootstrap CSS链接';
    }
    
    if (!strpos($content, $requiredElements['boxicons_css'])) {
        $fileIssues[] = '❌ 缺少标准Boxicons CSS链接';
    }
    
    if (!strpos($content, $requiredElements['fontawesome_css'])) {
        $fileIssues[] = '❌ 缺少Font Awesome CSS链接';
    }
    
    if (!strpos($content, $requiredElements['main_css'])) {
        $fileIssues[] = '❌ 缺少统一main.css文件';
    }
    
    // 检查导航栏
    if (!preg_match('/<nav[^>]*class[^>]*navbar navbar-expand-lg[^>]*>/i', $content)) {
        $fileIssues[] = '❌ 缺少标准导航栏结构';
    }
    
    // 检查容器
    if (!preg_match('/<div[^>]*class[^>]*container-fluid mt-4[^>]*>/i', $content)) {
        $fileIssues[] = '❌ 缺少标准容器结构';
    }
    
    // 检查禁止的元素
    if (strpos($content, $forbiddenElements['local_bootstrap'])) {
        $fileIssues[] = '⚠️ 使用了本地Bootstrap文件，应使用CDN';
    }
    
    if (strpos($content, $forbiddenElements['local_boxicons']) || 
        strpos($content, $forbiddenElements['double_slash_boxicons'])) {
        $fileIssues[] = '⚠️ 使用了本地Boxicons文件，应使用CDN';
    }
    
    // 检查内联样式过多
    $styleCount = preg_match_all('/<style[^>]*>.*?<\/style>/is', $content, $matches);
    if ($styleCount > 1) {
        $fileIssues[] = '⚠️ 内联样式过多，应使用统一CSS文件';
    }
    
    // 检查是否有重复的导航栏样式
    if (preg_match('/\.navbar\s*\{[^}]*background.*linear-gradient/is', $content)) {
        $fileIssues[] = '⚠️ 存在重复的导航栏样式定义';
    }
    
    // 检查是否有重复的卡片样式
    if (preg_match('/\.card\s*\{[^}]*border-radius.*15px/is', $content)) {
        $fileIssues[] = '⚠️ 存在重复的卡片样式定义';
    }
    
    // 检查页面标题格式
    if (!preg_match('/<title>[^<]+ - 公司数据总览系统<\/title>/i', $content)) {
        $fileIssues[] = '⚠️ 页面标题格式不统一';
    }
    
    if (empty($fileIssues)) {
        $passedFiles[] = $file;
    } else {
        $issues[$file] = $fileIssues;
    }
}

// 生成HTML报告
echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>最终风格验证报告 - 公司数据总览系统</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "    <link href='styles/main.css' rel='stylesheet'>\n";
echo "</head>\n";
echo "<body>\n";

echo "<nav class='navbar navbar-expand-lg'>\n";
echo "    <div class='container-fluid'>\n";
echo "        <a class='navbar-brand' href='#'>\n";
echo "            <i class='bx bx-check-shield me-2'></i>\n";
echo "            最终风格验证报告\n";
echo "        </a>\n";
echo "        <div class='navbar-nav ms-auto'>\n";
echo "            <span class='navbar-text text-white'>\n";
echo "                <i class='bx bx-time me-1'></i>\n";
echo "                验证时间: " . date('Y-m-d H:i:s') . "\n";
echo "            </span>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "</nav>\n";

echo "<div class='container-fluid mt-4'>\n";

// 统计信息
$totalFiles = count($phpFiles) - 2; // 排除验证脚本
$passedCount = count($passedFiles);
$failedCount = count($issues);
$successRate = round(($passedCount / $totalFiles) * 100, 1);

echo "<div class='row mb-4'>\n";
echo "    <div class='col-md-3'>\n";
echo "        <div class='card stat-card stat-card-primary'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-file-code stat-icon'></i>\n";
echo "                <h5 class='card-title'>总文件数</h5>\n";
echo "                <h2 class='card-text'>$totalFiles</h2>\n";
echo "                <p class='stat-info'>PHP文件总数</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "    <div class='col-md-3'>\n";
echo "        <div class='card stat-card stat-card-success'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-check-circle stat-icon'></i>\n";
echo "                <h5 class='card-title'>通过验证</h5>\n";
echo "                <h2 class='card-text'>$passedCount</h2>\n";
echo "                <p class='stat-info'>风格完全一致</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "    <div class='col-md-3'>\n";
echo "        <div class='card stat-card stat-card-" . ($failedCount > 0 ? 'warning' : 'success') . "'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-exclamation-triangle stat-icon'></i>\n";
echo "                <h5 class='card-title'>需要调整</h5>\n";
echo "                <h2 class='card-text'>$failedCount</h2>\n";
echo "                <p class='stat-info'>存在小问题</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "    <div class='col-md-3'>\n";
echo "        <div class='card stat-card stat-card-info'>\n";
echo "            <div class='card-body'>\n";
echo "                <i class='fas fa-percentage stat-icon'></i>\n";
echo "                <h5 class='card-title'>成功率</h5>\n";
echo "                <h2 class='card-text'>$successRate%</h2>\n";
echo "                <p class='stat-info'>风格一致性</p>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "</div>\n";

// 显示通过验证的文件
if (!empty($passedFiles)) {
    echo "<div class='card mb-4'>\n";
    echo "    <div class='card-header'>\n";
    echo "        <h5 class='card-title mb-0'><i class='fas fa-check-circle me-2'></i>完全符合标准的文件 ($passedCount)</h5>\n";
    echo "    </div>\n";
    echo "    <div class='card-body'>\n";
    echo "        <div class='row'>\n";
    foreach ($passedFiles as $file) {
        echo "            <div class='col-md-4 mb-2'>\n";
        echo "                <span class='badge bg-success me-2'><i class='fas fa-check me-1'></i>$file</span>\n";
        echo "            </div>\n";
    }
    echo "        </div>\n";
    echo "    </div>\n";
    echo "</div>\n";
}

// 显示需要调整的文件
if (!empty($issues)) {
    echo "<div class='card mb-4'>\n";
    echo "    <div class='card-header'>\n";
    echo "        <h5 class='card-title mb-0'><i class='fas fa-tools me-2'></i>需要微调的文件 ($failedCount)</h5>\n";
    echo "    </div>\n";
    echo "    <div class='card-body'>\n";
    foreach ($issues as $file => $fileIssues) {
        echo "        <div class='alert alert-warning'>\n";
        echo "            <h6><i class='fas fa-file-code me-2'></i><strong>$file</strong></h6>\n";
        echo "            <ul class='mb-0'>\n";
        foreach ($fileIssues as $issue) {
            echo "                <li>$issue</li>\n";
        }
        echo "            </ul>\n";
        echo "        </div>\n";
    }
    echo "    </div>\n";
    echo "</div>\n";
}

// 总结
echo "<div class='card'>\n";
echo "    <div class='card-header'>\n";
echo "        <h5 class='card-title mb-0'><i class='fas fa-clipboard-check me-2'></i>验证总结</h5>\n";
echo "    </div>\n";
echo "    <div class='card-body'>\n";
if ($successRate >= 90) {
    echo "        <div class='alert alert-success'>\n";
    echo "            <h6><i class='fas fa-trophy me-2'></i>优秀！</h6>\n";
    echo "            <p class='mb-0'>风格统一工作完成度达到 <strong>$successRate%</strong>，所有页面基本符合test_styles.html的标准风格。</p>\n";
    echo "        </div>\n";
} elseif ($successRate >= 70) {
    echo "        <div class='alert alert-info'>\n";
    echo "            <h6><i class='fas fa-info-circle me-2'></i>良好</h6>\n";
    echo "            <p class='mb-0'>风格统一工作完成度达到 <strong>$successRate%</strong>，大部分页面符合标准，少数页面需要微调。</p>\n";
    echo "        </div>\n";
} else {
    echo "        <div class='alert alert-warning'>\n";
    echo "            <h6><i class='fas fa-exclamation-triangle me-2'></i>需要改进</h6>\n";
    echo "            <p class='mb-0'>风格统一工作完成度为 <strong>$successRate%</strong>，还有较多页面需要调整。</p>\n";
    echo "        </div>\n";
}
echo "    </div>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
