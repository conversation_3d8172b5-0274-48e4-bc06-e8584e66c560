<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代打卡查询 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <style>
        /* 页面特定样式 */
        .problem-indicator {
            color: #dc3545;
            font-weight: bold;
        }

        .device-id {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .count-badge {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .alert-warning-custom {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 10px;
            color: #856404;
        }
    </style>
    <script language="JavaScript" type="text/javascript">
        var idTmr;
        function getExplorer() {
            var explorer = window.navigator.userAgent;
            if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
            } else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
            } else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
            } else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
            } else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
            }
        }
        
        function method5(tableid) {
            if(getExplorer()=='ie') {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                    var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                    print("Nested catch caught " + e);
                } finally {
                    oWB.SaveAs(fname);
                    oWB.Close(savechanges = false);
                    oXL.Quit();
                    oXL = null;
                    idTmr = window.setInterval("Cleanup();", 1);
                }
            } else {
                tableToExcel(tableid)
            }
        }
        
        function Cleanup() {
            window.clearInterval(idTmr);
            CollectGarbage();
        }
        
        var tableToExcel = (function() {
            var uri = 'data:application/vnd.ms-excel;base64,',
                template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
            return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
            }
        })()
    </script>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-shield-quarter me-2"></i>
                代打卡监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4"><?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
        ?>
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">开始日期:</label>
                    <input type="date" id="start-date" name="start-date" 
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">结束日期:</label>
                    <input type="date" id="end-date" name="end-date" 
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit" id="query-btn">
                        <i class="fas fa-search me-1"></i>查询数据
                    </button>
                    <button type="button" class="btn-export" onclick="method5('tableExcel')">
                        <i class="fas fa-file-excel me-1"></i>导出Excel
                    </button>
                </div>
            </form>
        </div>

        <!-- 检测说明 -->
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-warning-custom" role="alert">
                    <h4 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        代打卡检测说明
                    </h4>
                    <hr>
                    <p class="mb-0">
                        <strong>检测原理：</strong>通过分析同一设备串号被多个用户使用的情况，识别潜在的代打卡行为。
                        当多个员工使用相同设备打卡时，系统会自动标记为异常。
                    </p>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        统计时间范围：<?php echo date('Y年m月d日', strtotime($startDate)); ?> 至 <?php echo date('Y年m月d日', strtotime($endDate)); ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- 打卡问题数据清单 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            打卡异常数据清单
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="tableExcel">
                                <thead>
                                    <tr>
                                        <th width="15%">用户ID</th>
                                        <th width="20%">姓名</th>
                                        <th width="35%">设备串号</th>
                                        <th width="15%">打卡次数</th>
                                        <th width="15%">风险等级</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_records = 0;
                                    $high_risk_count = 0;
                                    
                                    $sql="SELECT
                                            t.uid AS 'id',
                                            t.device AS 'ch',
                                            COUNT(*) AS 'dkcs'
                                        FROM
                                            tuqoa_kqdkjl t
                                        JOIN (
                                            SELECT device
                                            FROM tuqoa_kqdkjl
                                            WHERE dkdt >= '$startDate' AND dkdt <= '$endDate'
                                            GROUP BY device
                                            HAVING COUNT(DISTINCT uid) > 1
                                        ) s ON t.device = s.device
                                        WHERE
                                            t.dkdt >= '$startDate' AND t.dkdt <= '$endDate'
                                        GROUP BY
                                            t.uid, t.device
                                        ORDER BY
                                            t.device, COUNT(*) DESC";
                                    $xm="";
                                    $result = mysqli_query($link, $sql);
                                    if ($result && mysqli_num_rows($result) > 0) {
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $total_records++;
                                            $sql1="SELECT name FROM `tuqoa_userinfo` WHERE `id`=".$row["id"];
                                            $result1 = mysqli_query($link, $sql1);
                                            if ($result1) {
                                                while ($row1 = mysqli_fetch_assoc($result1)) {
                                                    $xm=$row1["name"];
                                                }
                                            }
                                            
                                            // 风险等级评估
                                            $count = (int)$row["dkcs"];
                                            $risk_level = '';
                                            $risk_class = '';
                                            if ($count >= 20) {
                                                $risk_level = '高风险';
                                                $risk_class = 'bg-danger';
                                                $high_risk_count++;
                                            } elseif ($count >= 10) {
                                                $risk_level = '中风险';
                                                $risk_class = 'bg-warning';
                                            } else {
                                                $risk_level = '低风险';
                                                $risk_class = 'bg-info';
                                            }
                                    ?>
                                    <tr>
                                        <td><strong><?php echo $row["id"]; ?></strong></td>
                                        <td><?php echo $xm; ?></td>
                                        <td>
                                            <span class="device-id"><?php echo $row["ch"]; ?></span>
                                        </td>
                                        <td>
                                            <span class="count-badge"><?php echo $row["dkcs"]; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $risk_class; ?>"><?php echo $risk_level; ?></span>
                                        </td>
                                    </tr>
                                    <?php
                                        }
                                    } else {
                                    ?>
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="py-4">
                                                <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                                                <h5 class="mt-3 text-success">未发现异常打卡行为</h5>
                                                <p class="text-muted">在选定时间范围内，所有打卡记录均正常。</p>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计汇总 -->
        <?php if ($total_records > 0): ?>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 2.5rem;"></i>
                        <h3 class="mt-3"><?php echo $total_records; ?></h3>
                        <p class="text-muted">异常记录总数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-shield-alt text-danger" style="font-size: 2.5rem;"></i>
                        <h3 class="mt-3"><?php echo $high_risk_count; ?></h3>
                        <p class="text-muted">高风险用户数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-alt text-info" style="font-size: 2.5rem;"></i>
                        <h3 class="mt-3"><?php echo (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1; ?></h3>
                        <p class="text-muted">检测天数</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <?php
    mysqli_close($link);
    ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新最后更新时间
            updateLastUpdateTime();
            
            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);

            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function(e) {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    e.preventDefault();
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    e.preventDefault();
                    alert('开始日期不能大于结束日期');
                    return;
                }
            });

            // 添加表格动画效果
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    row.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });
    </script>
</body>
</html> 